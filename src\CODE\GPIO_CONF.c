/**
 * @file GPIO_CONF.h
 * @brief GPIO使用
 *
 * 设置IO口及处理IO状态
 *
 * @date 2025-07-16
 * <AUTHOR>
 */
#include "GPIO_CONF.h"
#include <zephyr/drivers/gpio.h>
#include <zephyr/device.h>
#include <zephyr/drivers/pwm.h>

#include "stdbool.h"

#include "System_Config.h"
#include "UART_COM.h"

#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(GPIO, LOG_LEVEL_DBG);

//唤醒IRQ 唤醒底层STC芯片
static const struct gpio_dt_spec Device_Sleep_Awaken = GPIO_DT_SPEC_GET(DT_ALIAS(awaken), gpios);//输出

//按键
static const struct gpio_dt_spec button_NRF = GPIO_DT_SPEC_GET_OR(DT_ALIAS(sw0), gpios, {0});
static const struct gpio_dt_spec button_STC = GPIO_DT_SPEC_GET_OR(DT_ALIAS(sw2), gpios, {0});
static struct gpio_callback button_cb_data_nrf;
static struct gpio_callback button_cb_data_stc;

#define DEBOUNCE_DELAY_MS 10 //10mS内不响应第二次

//OLED 
#define PWM_FREQ_HZ      10000    // 频率 3 kHz ~ 10 kHz  
#define PWM_PERIOD_NS    (1000000000 / PWM_FREQ_HZ)  
#define PWM_PULSE_WIDTH(duty)  (PWM_PERIOD_NS * (duty ? duty : 1) / 100) // 占空比,最小值为1%

#define PWM_OLED_Luminance    DT_ALIAS(oledluminance)
static const struct pwm_dt_spec LED_Luminance = PWM_DT_SPEC_GET(PWM_OLED_Luminance);
uint8_t test_oled =0;

static int64_t last_nrf_press = 0; 
static int64_t last_stc_press = 0;


/// @brief  设置唤醒IRQ IO状态
void GPIO_CONF_Set_Awaken(uint8_t flag)
{
  gpio_pin_set_dt(&Device_Sleep_Awaken, flag);
}

void button_pressed(const struct device *dev, struct gpio_callback *cb, uint32_t pins)
{
  int64_t now = k_uptime_get();

  if (pins & BIT(button_NRF.pin))
  {
    if (now - last_nrf_press > DEBOUNCE_DELAY_MS)
    {
      if (!gpio_pin_get_dt(&button_NRF))
      {
        LOG_DBG("NRF button UP");
      }
      else
      {
        LOG_DBG("NRF button Down");
        UART_COM_Send_SubDevice_Data(MARK_SEND,0x30,123,3);
      }
      last_nrf_press = now;
    }
  }

  if (pins & BIT(button_STC.pin))
  {
    if (now - last_stc_press > DEBOUNCE_DELAY_MS)
    {
      if (!gpio_pin_get_dt(&button_STC))
      {
        LOG_DBG("STC button UP");
      }
      else
      {
        LOG_DBG("STC button Down");
        test_oled ^=1;
        GPIO_CONF_OLED_Luminance_Set(test_oled);
      }
      last_stc_press = now;
    }
  }
}

/// @brief OLED亮度设置
/// @param duty 0-100%亮度
void GPIO_CONF_OLED_Luminance_Set(uint8_t duty)
{
  if (duty == 0)
  {
    pwm_set_dt(&LED_Luminance, PWM_PERIOD_NS, PWM_PERIOD_NS);  // 设置占空比为0来关闭PWM输出
    return;
  }
  pwm_set_dt(&LED_Luminance, PWM_PERIOD_NS, PWM_PULSE_WIDTH(duty));
}

/// @brief  GPIO初始化
void GPIO_CONF_Init(void)
{
  //唤醒IRQ 唤醒底层STC芯片
  if (!gpio_is_ready_dt(&Device_Sleep_Awaken))
  {
    LOG_ERR("LED GPIO device not ready");
    return;
  }
  gpio_pin_configure_dt(&Device_Sleep_Awaken, GPIO_OUTPUT_LOW);
  GPIO_CONF_Set_Awaken(~Config_Hardware_SubDevice_IRQ_GPIO_Trigger);
  //按键
  gpio_pin_configure_dt(&button_NRF, GPIO_INPUT);
  gpio_pin_interrupt_configure_dt(&button_NRF, GPIO_INT_EDGE_BOTH);//终端配置
  gpio_init_callback(&button_cb_data_nrf, button_pressed, BIT(button_NRF.pin));
  gpio_add_callback(button_NRF.port, &button_cb_data_nrf);

  gpio_pin_configure_dt(&button_STC, GPIO_INPUT);
  gpio_pin_interrupt_configure_dt(&button_STC, GPIO_INT_EDGE_BOTH);//终端配置
  gpio_init_callback(&button_cb_data_stc, button_pressed, BIT(button_STC.pin));
  gpio_add_callback(button_STC.port, &button_cb_data_stc);

  //OLED
  if (!pwm_is_ready_dt(&LED_Luminance))
  {
    LOG_ERR("Error: PWM device %s is not ready\n", LED_Luminance.dev->name);
    return ;
  }
}

