/**
 * @file Game_Common.h
 * @brief 玩法相关处理
 *
 *
 * @date 2025-07-07
 * <AUTHOR>
 */
#include "Game_Common.h"
#include <zephyr/sys_clock.h>
#include <zephyr/kernel.h>

#include "BLE_ATT_COM.h"
#include "central_and_peripheral.h"

ST_GAME_INFO GAME_INFO ={
  .Game_State = eInitial,
  .Game_Play.Time_Remaining = 0,
  .Game_Play.Time_Over = 0,
  .Game_Play.Hover_Dog.Goto_Mode_Index =0,
  .Game_Play.Hover_Dog.Goto_Mode_Dir_Flag = 1,
};
#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(GAME,  LOG_LEVEL_DBG);

#define GAME_LOOP_TIME_MS (uint8_t)(10) // 循环执行工作队列 10mS一次
struct k_work_delayable game_loop_work;  // 循环执行工作队列

typedef struct 
{
  uint8_t Triger_ID;//触发ID记录
}ST_Queue_Hover_Dog_ALL_ID_t;//巡回犬队列数据
//增加队列用于巡回犬模式下的分配
struct k_msgq Queue_Hover_Dog_ALL_ID;
static  uint8_t Queue_Hover_Dog_ALL_ID_buffer[BLE_CTL_Device_Connect_MAX*sizeof(ST_Queue_Hover_Dog_ALL_ID_t)];
static void Queue_Hover_Dog_ALL_ID_Init(void)
{
  k_msgq_init(&Queue_Hover_Dog_ALL_ID, Queue_Hover_Dog_ALL_ID_buffer, sizeof(ST_Queue_Hover_Dog_ALL_ID_t), BLE_CTL_Device_Connect_MAX);
}

/// @brief 获取一个数据 并且会自动写回去
/// @return -1:没数据 
static int8_t Get_Queue_Hover_Dog(void)
{
  uint8_t buff=0;
  if(k_msgq_get(&Queue_Hover_Dog_ALL_ID,&buff, K_NO_WAIT)  == -ENOMSG) //没数据了
  {
    return -1;
  }
  k_msgq_put(&Queue_Hover_Dog_ALL_ID,&buff, K_NO_WAIT);  // 放入队列内 用于离线模式下使用
  return buff;
}

/// @brief 将 x 从 [in_min, in_max] 映射到 [out_min, out_max]
static int16_t map_range(int16_t x, int16_t in_min, int16_t in_max, int16_t out_min, int16_t out_max)
{
  // 避免除以0
  if (in_max == in_min) return out_min;
  int32_t num = (int32_t)(x - in_min) * (out_max - out_min);
  int32_t den = (int32_t)(in_max - in_min);
  return (int16_t)(out_min + num / den);
}

/// @brief  获取往返模式下的下一个ID
/// @param device_num 
/// @return ID
int8_t Get_Goto_Mode_Queue_ID()
{
  int8_t get_queue_ID = 0;                               // 取ID
  if (GAME_INFO.Game_Play.Hover_Dog.Goto_Mode_Dir_Flag == 1)  // 顺+1
  {
    get_queue_ID = (uint8_t)Get_Queue_Hover_Dog();
    LOG_DBG("+1:%d", get_queue_ID);
  }
  else  // 逆N-1
  {
    uint8_t device_num=0;//有效设备数量
    device_num = BLE_INFO.CTL_FLASH.Valid_Count;
    if(device_num == 1)
    {
      get_queue_ID = (uint8_t)Get_Queue_Hover_Dog();
    }
    else
    {
      for(uint8_t i=0;i<device_num-1;i++)//设备数量不对 就多取一次 保证能取到有效值 取N-1
      {
        get_queue_ID = (uint8_t)Get_Queue_Hover_Dog();
      }
    } 
    LOG_DBG("N-1:%d", get_queue_ID);
  }
  // 更新方向
  if (GAME_INFO.Game_Play.Hover_Dog.Goto_Mode_Dir_Flag == 1 && GAME_INFO.Game_Play.Hover_Dog.Goto_Mode_Index >= BLE_INFO.CTL_FLASH.Valid_Count - 1)  // 顺+1
  {
    GAME_INFO.Game_Play.Hover_Dog.Goto_Mode_Dir_Flag = -1;
    LOG_DBG("Get_Goto_Mode_Queue_ID: +1");
  }
  else if (GAME_INFO.Game_Play.Hover_Dog.Goto_Mode_Dir_Flag == -1 && GAME_INFO.Game_Play.Hover_Dog.Goto_Mode_Index == 0)  // 逆+2
  {
    GAME_INFO.Game_Play.Hover_Dog.Goto_Mode_Dir_Flag = 1;
     LOG_DBG("Get_Goto_Mode_Queue_ID: -1");
  }
  GAME_INFO.Game_Play.Hover_Dog.Goto_Mode_Index += GAME_INFO.Game_Play.Hover_Dog.Goto_Mode_Dir_Flag;  // 更新索引
  return get_queue_ID;
}

/// @brief  游戏玩法初始化循环任务
static void Game_Comm_LOOP(struct k_work *work)
{
  static uint16_t time_count_1000mS=0;//1秒
  static uint8_t time_count_1minute=0;//1分钟
  time_count_1000mS++; 

  //-----------------------10MS一次------------------------------
  if (GAME_INFO.Game_Play.Hover_Dog.Start_EN == true && GAME_INFO.Game_Play.Hover_Dog.Mode_Start_Flag == true && GAME_INFO.Game_Play.Time_Over > 0)  // 使能 && 巡回犬玩法开启
  {
    // if(GAME_INFO.Game_Play.Hover_Dog.Triger_ID == GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Last)//触发了或者初始值 需要更新按键值
    //设计思路：通过循环队列的入队出队方式进行设计 减少边界控制
    int8_t get_queue_ID =0;
    int8_t conn_idex=-1;
    switch (GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type)  // 触发类型判断
    {
      case eNone:  // 初始值 分配第一个触发值
      {
        GAME_INFO.Game_Play.Hover_Dog.Number_Finish =0;
         if (BLE_INFO.CTL_FLASH.Valid_Count > 0)  // 要有设备才进行分配不然不知道会出现什么问题
         {
           // 从Flash中把所有设备ID取出来单独放列表内
           for (uint8_t num = 1; num <= BLE_CTL_Device_Connect_MAX; num++)  // 遍历触设备 用于离线模式下使用
           {
             if (BLE_INFO.CTL_FLASH.Valid_Device_BitFLag & (1 << (num-1)))  // 找到有效设备
               k_msgq_put(&Queue_Hover_Dog_ALL_ID, &num, K_NO_WAIT);    // 放入队列内 用于离线模式下使用
           }
         }
         else
         {
           LOG_ERR("Flash No Device???");
           return;
         }
         if (GAME_INFO.Game_Play.Hover_Dog.Mode == eGoTo)  // 往返模式
         {
           get_queue_ID = Get_Goto_Mode_Queue_ID();
         }
         else  // 随机 固定 模式下：n为一次
         {
           get_queue_ID = Get_Queue_Hover_Dog();  // 更新一下值
         }
        if (get_queue_ID < 0)
        {
          LOG_ERR("get_queue_ID < 0");
          return;
        }

        if (GAME_INFO.Game_Play.Hover_Dog.Off_Line_Mode_AI_OFF == true)  // 不考虑掉线的配件
        {
          // 在线发送 离线等待！！！
          conn_idex = BLE_ATT_Get_CTL_CONN_Index_Fo_SubDevice(get_queue_ID);//拿蓝牙索引 拿不到设备可能不在线
          if (conn_idex >= 0)  // 有值
          {
            GAME_INFO.Game_Play.Hover_Dog.Triger_ID = get_queue_ID;
            // 肩灯设置0x70+ID
            uint8_t send_buff[2];
            send_buff[0]         = 0x70;
            send_buff[1]         = get_queue_ID;
            BLE_CTL_Write(BLE_INFO.CTL_CONN[conn_idex].CONN, send_buff, 2);  // 设置肩灯
            GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type = eAwaitTrigger;    // 等待触发
            LOG_DBG("eNone Triger_ID:%d", get_queue_ID);
          }
          else//不在线
          {
            LOG_WRN("eNone Off_Line_Mode");
            GAME_INFO.Game_Play.Hover_Dog.Triger_ID = get_queue_ID;
            GAME_INFO.Game_Play.Hover_Dog.Off_Line_ID = get_queue_ID;//记录离线ID
            GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type = eAwaitNotify;  // 设备离线等待通知
          }
        }
        else  // 智能跳过离线设备
        {
          for (uint8_t num = 1; num <= BLE_CTL_Device_Connect_MAX; num++)  // 检测所有在线设备
          {
            conn_idex = BLE_ATT_Get_CTL_CONN_Index_Fo_SubDevice(get_queue_ID);  // 拿蓝牙索引
            if (conn_idex >= 0)  // 有值
            {
              GAME_INFO.Game_Play.Hover_Dog.Triger_ID = get_queue_ID;
              // 肩灯设置0x70+ID
              uint8_t send_buff[2];
              send_buff[0]         = 0x70;
              send_buff[1]         = get_queue_ID;
              BLE_CTL_Write(BLE_INFO.CTL_CONN[conn_idex].CONN, &send_buff[0], 2);  // 设置肩灯
              GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type = eAwaitTrigger;    // 等待触发
              LOG_DBG("AI eNone conn_idex:%d,Triger_ID:0X%2X", conn_idex,get_queue_ID);
              break;
            }
            else
            {
              if (GAME_INFO.Game_Play.Hover_Dog.Mode == eGoTo)  // 往返模式
              {
                get_queue_ID = Get_Goto_Mode_Queue_ID();
              }
              else  // 随机 固定 模式下：n为一次
              {
                get_queue_ID = Get_Queue_Hover_Dog();  // 更新一下值
              }
            }
          }
        }
      }
      break;
      case eAwaitTrigger:  // 等待触发
      {
        // 等异步触发
      }
      break;
      case eSubDevice:  // 子设备触发
      {
        int8_t get_queue_ID = 0;
        //---------------------取消肩灯显示-----------------------------------
        uint8_t send_buff[2];                     
        int8_t conn_idex     = BLE_ATT_Get_CTL_CONN_Index_Fo_SubDevice(GAME_INFO.Game_Play.Hover_Dog.Triger_ID);//取触发索引进行关灯
        if (conn_idex > -1)
        {
          send_buff[0] = 0x70;
          send_buff[1] = 0;
          BLE_CTL_Write(BLE_INFO.CTL_CONN[conn_idex].CONN, send_buff, 2);  // 取消肩灯显示
          LOG_DBG("eSubDevice Triger_ID:%d", GAME_INFO.Game_Play.Hover_Dog.Triger_ID);
        }
        //---------------------分配新的按键灯-----------------------------------
        GAME_INFO.Game_Play.Hover_Dog.Number_Finish++;   // 完成一个周期 两两间算一个单程即 按键次数-1
        if (GAME_INFO.Game_Play.Hover_Dog.Mode == eGoTo)  // 往返模式
        {
          get_queue_ID = Get_Goto_Mode_Queue_ID();
        }
        else  // 随机 固定 模式下：n为一次
        {
          if (GAME_INFO.Game_Play.Hover_Dog.Mode == eRandom)  // 随机
          {
            uint8_t Triger_ID = (uint8_t)k_uptime_get_32() & 0x0F;
            Triger_ID %= BLE_INFO.CTL_FLASH.Valid_Count;  // 随机取一个设备ID出来
            if (Triger_ID != 0)
            {
              for (; Triger_ID > 0; Triger_ID--)  // 随机一个值
              {
                get_queue_ID = (uint8_t)Get_Queue_Hover_Dog();
              }
            }
            if (get_queue_ID == GAME_INFO.Game_Play.Hover_Dog.Triger_ID)  // 触发的和随机的一样就换下一个值
            {
              get_queue_ID = (uint8_t)Get_Queue_Hover_Dog();
            }
          }
          else//队列本就是循环模式
          {
            get_queue_ID = (uint8_t)Get_Queue_Hover_Dog();
          }
        }
        //---------------------开锁条件达成-----------------------------------
        if (GAME_INFO.Game_Play.Hover_Dog.Number_Finish > GAME_INFO.Game_Play.Hover_Dog.Number_Config )  // 判断是不是到达开锁
        {
          GAME_INFO.Game_Play.Hover_Dog.Number_Finish = 0;
          GAME_INFO.Game_Play.Time_Over               = 0;  // 开锁
          // 清空一些参数
          GAME_INFO.Game_Play.Hover_Dog.Goto_Mode_Index = 0;// 往返模式下当前索引
          GAME_INFO.Game_Play.Hover_Dog.Goto_Mode_Dir_Flag= 1;// 往返模式当前方向（+1 或 -1）

          for (uint8_t num = 1; num <= BLE_CTL_Device_Connect_MAX; num++)  // 清空
          {
            uint8_t buff;
            if (k_msgq_get(&Queue_Hover_Dog_ALL_ID, &buff, K_NO_WAIT) == -ENOMSG)  // 没数据了
            {
              break;
            }
          }
          GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type = eNone;  // 重新分配
          break;
        }
        else if (GAME_INFO.Game_START_STOP_CONF.Data.Strength_A != 0xff || GAME_INFO.Game_START_STOP_CONF.Data.Strength_B != 0xff)  // 打开了APP的强度设置 这里模拟会减去对应百分比的剩余定时时间
        {
          if (GAME_INFO.Game_Play.Hover_Dog.Number_Finish > 1)  // 首次按下不计入
          {
            LOG_DBG("Number_Finish:%d",GAME_INFO.Game_Play.Hover_Dog.Number_Finish);
            LOG_DBG("Time_Over:%d * %d / %d",GAME_INFO.Game_Play.Time_Over, (GAME_INFO.Game_Play.Hover_Dog.Number_Finish-1),GAME_INFO.Game_Play.Hover_Dog.Number_Config);
            GAME_INFO.Game_Play.Time_Over = GAME_INFO.Game_Play.Time_Over * (GAME_INFO.Game_Play.Hover_Dog.Number_Config - (GAME_INFO.Game_Play.Hover_Dog.Number_Finish-1)) / GAME_INFO.Game_Play.Hover_Dog.Number_Config;
          }
        }
        //---------------------发送肩灯ID-----------------------------------
        if (get_queue_ID < 0)
        {
          LOG_ERR("eRandom get_queue_ID < 0");//不应该出现没有值
          return;
        }
        if (GAME_INFO.Game_Play.Hover_Dog.Off_Line_Mode_AI_OFF == true)  // 不考虑掉线的配件
        {
          // 在线发送 离线等待！！！
          conn_idex = BLE_ATT_Get_CTL_CONN_Index_Fo_SubDevice(get_queue_ID);//拿蓝牙索引 拿不到设备可能不在线
          if (conn_idex >= 0)  // 有值
          {
            GAME_INFO.Game_Play.Hover_Dog.Triger_ID = get_queue_ID;
            // 肩灯设置0x70+ID
            uint8_t send_buff[2];
            send_buff[0]         = 0x70;
            send_buff[1]         = get_queue_ID;
            BLE_CTL_Write(BLE_INFO.CTL_CONN[conn_idex].CONN, send_buff, 2);  // 设置肩灯
            GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type = eAwaitTrigger;    // 等待触发
            LOG_DBG("eNone Triger_ID:%d", get_queue_ID);
          }
          else//不在线
          {
            LOG_WRN("eSubDevice Off_Line_Mode");
            GAME_INFO.Game_Play.Hover_Dog.Triger_ID = get_queue_ID;
            GAME_INFO.Game_Play.Hover_Dog.Off_Line_ID = get_queue_ID;//记录离线ID
            GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type = eAwaitNotify;  // 设备离线等待通知
          }
        }
        else // 智能跳过
        {
          for (uint8_t num = 1; num <= BLE_CTL_Device_Connect_MAX; num++)  // 检测所有在线设备
          {
            conn_idex = BLE_ATT_Get_CTL_CONN_Index_Fo_SubDevice(get_queue_ID);  // 拿蓝牙索引
            if (conn_idex >= 0)  // 有值
            {
              GAME_INFO.Game_Play.Hover_Dog.Triger_ID = get_queue_ID;
              // 肩灯设置0x70+ID
              uint8_t send_buff[2];
              send_buff[0]         = 0x70;
              send_buff[1]         = get_queue_ID;
              BLE_CTL_Write(BLE_INFO.CTL_CONN[conn_idex].CONN, send_buff, 2);  // 设置肩灯
              GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type = eAwaitTrigger;    // 等待触发
              LOG_DBG("eRandom Triger_ID:%d", get_queue_ID);
              break;
            }
            else
            {
              if (GAME_INFO.Game_Play.Hover_Dog.Mode == eGoTo)  // 往返模式
              {
                get_queue_ID = Get_Goto_Mode_Queue_ID();
              }
              else  // 随机 固定 模式下：n为一次
              {
                get_queue_ID = Get_Queue_Hover_Dog();  // 更新一下值
              }
            }
          }
        }
      }
      break;
      case eDisconnect:  // 断开连接
      {
        //正在触发设备断开才在这处理
        if (GAME_INFO.Game_Play.Hover_Dog.Off_Line_Mode_AI_OFF == true)  // 不考虑掉线的配件
        {
          GAME_INFO.Game_Play.Hover_Dog.Off_Line_ID = GAME_INFO.Game_Play.Hover_Dog.Triger_ID;//记录离线ID
          GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type = eAwaitNotify;  // 设备离线等待通知
        }
        else//跳过 进行下一个
        {
          if (GAME_INFO.Game_Play.Hover_Dog.Mode == eGoTo)  // 往返模式
          {
            get_queue_ID = Get_Goto_Mode_Queue_ID();
          }
          else  // 随机 固定 模式下：n为一次
          {
            if (GAME_INFO.Game_Play.Hover_Dog.Mode == eRandom)  // 随机
            {
              uint8_t Triger_ID = (uint8_t)k_uptime_get_32() & 0x0F;
              Triger_ID %= BLE_INFO.CTL_FLASH.Valid_Count;  // 随机取一个设备ID出来
              if (Triger_ID != 0)
              {
                for (; Triger_ID > 0; Triger_ID--)  // 随机一个值
                {
                  get_queue_ID = (uint8_t)Get_Queue_Hover_Dog();
                }
              }
              if (get_queue_ID == GAME_INFO.Game_Play.Hover_Dog.Triger_ID)  // 触发的和随机的一样就换下一个值
              {
                get_queue_ID = (uint8_t)Get_Queue_Hover_Dog();
              }
            }
            else  // 队列本就是循环模式
            {
              get_queue_ID = (uint8_t)Get_Queue_Hover_Dog();
            }
          }

          for (uint8_t num = 1; num <= BLE_CTL_Device_Connect_MAX; num++)  // 检测所有在线设备
          {
            conn_idex = BLE_ATT_Get_CTL_CONN_Index_Fo_SubDevice(get_queue_ID);  // 拿蓝牙索引
            if (conn_idex >= 0)  // 有值
            {
              GAME_INFO.Game_Play.Hover_Dog.Triger_ID = get_queue_ID;
              // 肩灯设置0x70+ID
              uint8_t send_buff[2];
              send_buff[0]         = 0x70;
              send_buff[1]         = get_queue_ID;
              BLE_CTL_Write(BLE_INFO.CTL_CONN[conn_idex].CONN, send_buff, 2);  // 设置肩灯
              GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type = eAwaitTrigger;    // 等待触发
              LOG_DBG("eRandom Triger_ID:%d", get_queue_ID);
              break;
            }
            else
            {
              if (GAME_INFO.Game_Play.Hover_Dog.Mode == eGoTo)  // 往返模式
              {
                get_queue_ID = Get_Goto_Mode_Queue_ID();
              }
              else  // 随机 固定 模式下：n为一次
              {
                get_queue_ID = Get_Queue_Hover_Dog();  // 更新一下值
              }
            }
          }
        }
      }
      break;
      case eAwaitNotify:  // 设备离线等待通知
      {
        get_queue_ID = GAME_INFO.Game_Play.Hover_Dog.Off_Line_ID;
        conn_idex = BLE_ATT_Get_CTL_CONN_Index_Fo_SubDevice(get_queue_ID);  // 拿蓝牙索引
        if (conn_idex >= 0 && BLE_MACList_Check(&BLE_List_Validity,&BLE_INFO.CTL_CONN[conn_idex].mac) != true)  // 有值 && 当前MAC通过了合法性验证才发送消息
        {
          GAME_INFO.Game_Play.Hover_Dog.Triger_ID = get_queue_ID;
          // 肩灯设置0x70+ID
          uint8_t send_buff[2];
          send_buff[0]         = 0x70;
          send_buff[1]         = get_queue_ID;
          BLE_CTL_Write(BLE_INFO.CTL_CONN[conn_idex].CONN, send_buff, 2);  // 设置肩灯
          GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type = eAwaitTrigger;    // 等待触发
        }
      }
      break;
      default:
        break;
    }
  }
  else if(GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type != eNone)
  {
    GAME_INFO.Game_Play.Hover_Dog.Goto_Mode_Index = 0;// 往返模式下当前索引
    GAME_INFO.Game_Play.Hover_Dog.Goto_Mode_Dir_Flag= 1;// 往返模式当前方向（+1 或 -1）

    for (uint8_t num = 1; num <= BLE_CTL_Device_Connect_MAX; num++)  // 清空
    {
      uint8_t buff;
      if (k_msgq_get(&Queue_Hover_Dog_ALL_ID, &buff, K_NO_WAIT) == -ENOMSG)  // 没数据了
      {
        break;
      }
    }
    GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type = eNone;  // 重新分配
    LOG_DBG("eNone Triger_ID_Type");
  }
  //----------------------一秒一次-------------------------------
  if(time_count_1000mS >= (1000 / GAME_LOOP_TIME_MS)) 
  {
    // LOG_DBG("Hover_Dog Status: Start_EN=%d, Mode_Start_Flag=%d, Time_Over=%d", 
    //         GAME_INFO.Game_Play.Hover_Dog.Start_EN,
    //         GAME_INFO.Game_Play.Hover_Dog.Mode_Start_Flag,
    //         GAME_INFO.Game_Play.Time_Over);
    time_count_1000mS=0;
    time_count_1minute++;//1分钟
    if (GAME_INFO.Game_State == eUndeway || GAME_INFO.Game_State == eDelay)  // 生效中 || 延迟 上报APP
    {
      if (GAME_INFO.Game_State == eUndeway)  // 生效中
      {
        if(GAME_INFO.Game_Play.Time_Over_Stop_Falg == false)//不停止计数
        {
          if (GAME_INFO.Game_Play.Time_Over > 0 )  // 开锁时间-- 
          {
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State, GAME_INFO.Game_Play.Time_Over), 4);
            GAME_INFO.Game_Play.Time_Over--;
          }
          else
          {
            GAME_INFO.Game_State = eInitial;
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State, 0), 4);
          }
          
          if (GAME_INFO.Game_Play.Time_Remaining > 0 && GAME_INFO.Game_Play.Time_Remaining != 0xffff)  // 剩余游戏时间--
          {
            GAME_INFO.Game_Play.Time_Remaining--;
            if(GAME_INFO.Game_Play.Time_Remaining == 0)
            {
              GAME_INFO.Game_State = eInitial;
              BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State, 0), 4);
              GAME_INFO.Game_Play.Time_Over =0;
            }
          }
        }
        
        if( GAME_INFO.Game_Play.Time_Over_Mode == eAddition)// 固定增加速度
        {
          if((GAME_INFO.Game_Play.Time_Over+ GAME_INFO.Game_Play.Time_Over_Addition_Parameter) >= GAME_INFO.Game_Play.Time_Remaining)//不能大于总游戏时间
          {
            GAME_INFO.Game_Play.Time_Over = GAME_INFO.Game_Play.Time_Remaining;
          }
          else
          {
            GAME_INFO.Game_Play.Time_Over += GAME_INFO.Game_Play.Time_Over_Addition_Parameter;
          }
          BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State, GAME_INFO.Game_Play.Time_Over), 4);
        }
        else if(GAME_INFO.Game_Play.Time_Over_Mode == eAddition_Parameter)//参数越大，剩余时间增加越快
        {
          if (GAME_INFO.Game_Play.Time_Over_Addition_Parameter >= 0)//正值急速增加
          {
            uint8_t acc_value = map_range(GAME_INFO.Game_Play.Time_Over_Addition_Parameter,1,40,2,80);
            if((GAME_INFO.Game_Play.Time_Over + acc_value) >= GAME_INFO.Game_Play.Time_Remaining)//不能大于总游戏时间
            {
              GAME_INFO.Game_Play.Time_Over = GAME_INFO.Game_Play.Time_Remaining;
            }
            else
            {
              GAME_INFO.Game_Play.Time_Over += acc_value;
            }
          }
          else
          {
          }
          BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State, GAME_INFO.Game_Play.Time_Over), 4);
        }
      }
      else//延迟 中
      {
        if (GAME_INFO.Game_Play.Time_Delay_Game_Start > 0)  // 延迟时间--
        {
          BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State, GAME_INFO.Game_Play.Time_Delay_Game_Start), 4);
          GAME_INFO.Game_Play.Time_Delay_Game_Start--;
        }
        else if(GAME_INFO.Game_Play.Time_Over > 0)//延迟完毕 开始生效
        {
          GAME_INFO.Game_State = eUndeway;
          BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State, GAME_INFO.Game_Play.Time_Over), 4);
          GAME_INFO.Game_Play.Time_Over--;
        }
      }
    }
  }
  /*一分钟一次*/
  if (time_count_1minute >= 60)
  {
    time_count_1minute=0;
    if (GAME_INFO.Game_State == eUndeway || GAME_INFO.Game_State == eDelay)  // 生效中 || 延迟 上报APP
    {
      if(GAME_INFO.Game_Play.Time_Over > 60) //1分钟上报一次
      {
        if (GAME_INFO.Game_State == eUndeway)  // 生效中
        {
          BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State, GAME_INFO.Game_Play.Time_Over), 4);
        }
        else
        {
          BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State, GAME_INFO.Game_Play.Time_Delay_Game_Start), 4);
        }
      }
    }
  }
  k_work_schedule(&game_loop_work, K_MSEC(GAME_LOOP_TIME_MS));  // 启动游戏玩法工作队列
}

/// @brief 游戏玩法初始化
void Game_Common_Init(void)
{
  k_work_init_delayable(&game_loop_work, Game_Comm_LOOP);       // 初始化游戏玩法工作队列
  k_work_schedule(&game_loop_work, K_MSEC(GAME_LOOP_TIME_MS));  // 启动游戏玩法工作队列
  Queue_Hover_Dog_ALL_ID_Init();//初始化静态队列 用于存储分配值
}