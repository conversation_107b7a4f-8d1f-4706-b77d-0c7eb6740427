#ifndef CENTRAL_AND_PERIPHERAL_H_
#define CENTRAL_AND_PERIPHERAL_H_
#include <errno.h>
#include <stddef.h>
#include <zephyr/bluetooth/bluetooth.h>
#include <zephyr/bluetooth/conn.h>
#include <zephyr/bluetooth/gatt.h>
#include <zephyr/bluetooth/hci.h>
#include <zephyr/bluetooth/uuid.h>
#include <zephyr/kernel.h>
#include <zephyr/sys/byteorder.h>
#include <zephyr/sys/printk.h>
#include <zephyr/types.h>
#include "BLE_MACList.h"
#include "BLE_ATT_COM.h"

#define BLE_Euipment_ID (uint8_t)(0x10)
// #define DGLAB_ID_DEVICE_TYPE     0x40  // 子设备类型
#define DGLAB_ID_DEVICE_PROJECT  0x03  // 设备型号
#define DGLAB_ID_APP_STEP        0x09  // APP迭代号
#define DGLAB_ID_UI_STEP         0x02  // APPUI资源迭代号
#define DGLAB_ID_DIS_BUILD (const uint8_t[]) { DGLAB_ID_APP_STEP, DGLAB_ID_DEVICE_PROJECT }  // 读取蓝牙0x1501特性得到的发行版本号 详见 https://dungeon-lab.feishu.cn/wiki/LwQnweJlAiF72WkJJ6NcqZWwnkg

#define BLE_Scan_RSSI_TH (int8_t)(-60)  // 扫描RSSI阈值，低于此值不连接设备
#define BLE_Scan_Select_Name (const uint8_t[]){"47L12"}  // 扫描的设备名称前缀符合即连接

//----这里是移植气压计的ble_globalDefines.h文件---
/* USER - NAME */
#define DGLAB_DEVICE_NAME_APP "47L121000"     // 正常工作(APP)状态时的设备名称
#define DGLAB_DEVICE_NAME_OTA "47L121000_O4"  // OTA时的设备名称为正常模式名称后追加后缀"_O{OTA版本:X}" (:X表示使用十六进制)
// 根据[OTA通信协议](https://dungeon-lab.feishu.cn/wiki/SEWxwx42IiyYqAkTz6WcKguDndc)
// NOTE: O2到O3修改了密钥, 不兼容更新
// NOTE: O3到O4加强了密钥混淆, 不兼容更新
//---这里是移植气压计的ble_globalDefines.h文件 END---


//-------------------------------CTL中央设备连接信息------------------------------------
#define BLE_CTL_Device_Connect_MAX 6 // 最大连接数

// #define OMS_UUID_Server 0x180C
#if 1
#define BLE_CTL_DGLAB_CHR_RX_UUID (uint16_t)0x150B  //notify | read
#define BLE_CTL_DGLAB_CHR_TX_UUID (uint16_t)0x150A  //write
#else
#define BLE_CTL_DGLAB_CHR_RX_UUID (uint16_t)0xff01  //notify | read
#define BLE_CTL_DGLAB_CHR_TX_UUID (uint16_t)0xff02  //write
#endif

#define SCAN_INTERVAL              BT_GAP_SCAN_FAST_INTERVAL_MIN /* 30 ms */
#define SCAN_WINDOW                BT_GAP_SCAN_FAST_WINDOW /* 30 ms */
#define INIT_INTERVAL              0x0010 /* 10 ms */
#define INIT_WINDOW                0x0010 /* 10 ms */
#define CONN_INTERVAL              20
#define CONN_LATENCY               0
#define CONN_TIMEOUT MIN(MAX((CONN_INTERVAL * 125 * MAX(CONFIG_BT_MAX_CONN, 6) / 1000), 10), 3200) /* 连接超时 */

typedef void (*BLE_CTL_notify_callback_t)(struct bt_conn *conn, const void *data, uint16_t length);  // 定义中央设备通知回调函数类型
typedef struct
{
  struct k_work validity_check_work;  // 安全性检查工作队列
  BLE_CTL_notify_callback_t Notify_CB;  // 通知回调函数
} ST_CTL_FUNC_CB;


typedef enum
{
  eNone_SCAN=0,   //无扫描
  eActive_SCAN,   //主动扫描
  eAuto_SCAN,     //自动扫描
}CTL_SCAN_MODE;//扫描模式




typedef struct _ble_info 
{
  struct bt_conn *CONN;
  uint8_t volatile conn_count; /* 当前连接数 */
  uint8_t Index_MAX;
  uint8_t Index;
  uint8_t Index_MAP[CONFIG_BT_MAX_CONN];//Index 映射
  CTL_SCAN_MODE Scan_Mode;        //扫描模式
  uint8_t Disconn_Scan_EN_Flag:1; // 断开后扫描标志
  uint8_t Auto_SCAN_Flag:1;       // 自动扫描标志

  uint8_t Validity_BitFlag;       //位标记 减少计算
  struct
  {
    uint8_t conn_index;           // 设备安全性索引，用于标记需要安全验证设备 异步操作
    uint8_t SendNum_Count;        // 发次数累计
    uint8_t Device_NEW_Flag : 1;  // 新设备标志
    uint8_t DelaySendTime_x100ms; // 延迟发送时间
    bt_addr_le_t MAC;             //用于检测当前数据是否存在
    uint8_t Data[17];//验证发送的数据
  }Validity_Chack[CONFIG_BT_MAX_CONN];//合法性验证

  struct
  {
    uint8_t Await_Updata_Flag : 1;  // 等待更新数据
    uint8_t Count_Num_Max;          // 用于数据计数最大值
    uint8_t Count_Num;              // 用于数据计数
    bt_addr_le_t MAC[BLE_CTL_Device_Connect_MAX];  // 离线设备MAC地址记录 用于重连
  } OFFLINE;// 离线重连设备
} ST_CTL_CONF; // 中央节点下的设置参数

typedef struct
{
  struct bt_conn *CONN;
  int8_t state;  // 连接状态标志
  uint8_t index;  // 连接索引
  bt_addr_le_t mac;  // 目标设备的MAC地址
  struct bt_uuid_16 discover_uuid;  // 用于服务发现的UUID
  struct bt_gatt_discover_params discover_params;  // GATT发现参数
  struct bt_gatt_subscribe_params subscribe_params;  // GATT订阅参数
  uint16_t gatt_write_handle;  // GATT写入参数
} ST_CTL_CONN_INFO;

struct _ctl_flash_info//保存在FLASH内 的信息
{
  uint8_t Flash_ID_Num;          // 存储ID号 0可用
  eSubDevice_Type Device_Type;   // 设备类型
  bt_addr_le_t MAC;              // 设备MAC地址
  uint8_t Device_Type_Data_LEN;  // 设备类型数据长度
  union 
  {
    uint8_t All_Arrays[32];//总字节大小，用于存储设备信息
    struct
    {
      uint8_t eSubDevice_Color;  // 颜色信息（1字节）
      uint8_t Config[15];        // 触发设置（15字节）
      uint8_t SOC;               // 电量百分比
    } Button; 
  } Type_Data;  // 设备类型数据
};
typedef struct// 存储蓝牙的信息 
{
  uint8_t Valid_Count;           // 有效设备数量 按顺序存信息
  uint8_t Valid_Device_BitFLag;  // 有效设备位标志 (8位) 每一位代表一个设备是否有效 给灯分配颜色用
  struct _ctl_flash_info INFO_Array[BLE_CTL_Device_Connect_MAX];// 中央设备保存连接过的设备信息参数
}ST_CTL_FLASH;  

/// @brief 通过MAC地址直接连接设备
int BLE_CTL_connect_by_mac(const bt_addr_le_t *addr);
/// @brief 中央设备连接回调
void BLE_CTL_GATT_FUNC_CB_Init(BLE_CTL_notify_callback_t notify_cb);
/// @brief BLE开始扫描的函数
void BLE_CTL_LE_start_scan_begin(void);
/// @brief 启动UUID服务查询
void BLE_CLT_start_service_discovery(struct bt_conn *conn);
/// @brief 发送数据到中央设备下数据
int BLE_CTL_Write(struct bt_conn *conn,const uint8_t *data, uint16_t len);

//-------------------------------PPL外围设备信息------------------------------------
/* USER - UUID */

#define DGLAB_SVC_OMS_UUID      BT_UUID_DECLARE_16(0x180C)   // DG-LAB 业务通信串口服务
#define DGLAB_CHR_RX_UUID       BT_UUID_DECLARE_16(0x150A)   // 接收APP指令, write without response
#define DGLAB_CHR_TX_UUID       BT_UUID_DECLARE_16(0x150B)   // 上报、回应, read | notify 响应头(0x53)

#define DGLAB_SVC_INFO_UUID     BT_UUID_DECLARE_16(0x180A)   // DG-LAB 设备信息服务 
#define DGLAB_CHR_HWCD_UUID     BT_UUID_DECLARE_16(0x1501)   // DG-LAB 硬件版本识别码       read
#define DGLAB_CHR_HENV_UUID     BT_UUID_DECLARE_16(0x1500)   // DG-LAB 硬件环境信息(电量等) read | notify

#define DGLAB_SVC_OTA_UUID      BT_UUID_DECLARE_16(0xFF0A)   // DG-LAB 设备升级服务
#define DGLAB_CHR_DATA_UUID     BT_UUID_DECLARE_16(0xFF01)   // DG-LAB 固件传输通道  write
#define DGLAB_CHR_SYNC_UUID     BT_UUID_DECLARE_16(0xFF00)   // DG-LAB 分包同步通道  notify |write

// 外围设备回调类型
typedef enum
{
  ePPL_CB_TYPE_Write_OMS_RX = 0,  // OMS 写服务回调   0x150A
  ePPL_CB_TYPE_Write_OTA_DATA,    // OTA 数据写入回调
  ePPL_CB_TYPE_Write_OTA_SYNC,    //  OTA 同步写入回调

  ePPL_CB_TYPE_Read_OMS_TX,         // OMS 读服务回调
  ePPL_CB_TYPE_Read_OMS_TX_AUTHEN,  // OMS 读服务回调(需要认证)
  ePPL_CB_TYPE_Read_INFO_HWCD,      // 设备硬件版本识别码读取回调 0x1501
  ePPL_CB_TYPE_Read_INFO_HENV,      // 设备硬件环境信息读取回调 0x1500

  ePPL_CB_TYPE_Notify_OMS_TX,     // OMS 通知服务回调 0x150B
  ePPL_CB_TYPE_Notify_INFO_HENV,  // 设备硬件环境信息通知回调
  ePPL_CB_TYPE_Notify_OTA_SYNC,   // OTA 同步通知回调

} ePPL_CB_Type_t;

typedef void (*BLE_PPL_Write_callback_t)(ePPL_CB_Type_t _type, const uint8_t *const _data, uint16_t *_len);  // 定义外围设备写入回调函数类型
typedef ssize_t (*BLE_PPL_Read_callback_t)(ePPL_CB_Type_t _type, struct bt_conn *conn, const struct bt_gatt_attr *attr, void *buf, uint16_t* buf_len, uint16_t* offset);  // 定义外围设备读取回调函数类型

typedef struct 
{
  BLE_PPL_Write_callback_t  Write_CB;     // 服务写入回调函数
  BLE_PPL_Read_callback_t   Read_CB;      // 服务读取回调函数
}ST_PPL_FUNC_CB;  //回调函数

/** @brief mos send status. */
enum bt_mos_send_status
{
  /** Send notification enabled. */
  BT_mos_SEND_STATUS_ENABLED,
  /** Send notification disabled. */
  BT_mos_SEND_STATUS_DISABLED,
};

typedef struct
{
  struct bt_conn *CONN;  // 连接句柄
  struct k_work adv_work;  // 广播工作队列
  struct bt_gatt_exchange_params exchange_params;  // MTU交换参数
  struct 
  {
    uint16_t GATT_OMS;
    uint16_t GATT_INFO;
    uint16_t GATT_OTA;
  }CCC_EN;
  struct
  {// 移植气压计machine_ble.h下
    bool flag_enabled;       // BLE外设使能标识
    // bool flag_connected;     // BLE已被连接标识
    bool flag_usePublicAdv;  // 当前是否使用公共广播
  // ble_peerStatus_t* peerStatus;
  // status_dglab_chr_t dglab_chr[Idx_handle_dglab_max];
  // char     name_bleADV[24];
  // struct
  // {
  //    uint8_t code[12];  // 上位机指定的会话ID, 约定长度为6~10字节
  //    uint8_t len;       // 会话ID码的长度, 如果为0则不启用
  // } seesionID;
  } G_Status_BLE;
  struct
  {
    uint8_t MANUFACTURER_DATA[10];     //(变量) 由上位机设置的6~10字节
    uint8_t MANUFACTURER_DATA_LEN;    // (变量) 上位机设置的MANUFACTURER_DATA的实际长度
  }ADV;
  
} ST_PPL_CONN_INFO;


void adv_work_handler(struct k_work *work);  // 开始广播的函数
// #if defined(CONFIG_BT_GATT_CLIENT)
// int mtu_exchange(struct bt_conn *conn);
// #endif
void update_phy(struct bt_conn *conn);
void update_mtu(struct bt_conn *conn);

/// @brief  外围设备GATT读写回调函数
void BLE_PPL_GATT_FUNC_CB_Init(BLE_PPL_Write_callback_t write_cb, BLE_PPL_Read_callback_t read_cb);
/// @brief  通知发送函数
int BLE_PPL_Write(ePPL_CB_Type_t _type, const uint8_t *data, uint16_t len);

/// @brief  启动广播工作队列
void advertising_start(void);
/// @brief  初始化外围设备的GATT服务
void BLE_PPL_Gatt_Services_Init(void);
/// @brief  删除外围设备的GATT服务
void BLE_PPL_Gatt_Services_Delete(struct bt_gatt_service *_service);



//-------------------------------中央&外围设备信息------------------------------------

typedef struct
{
  ST_CTL_CONF CTL_CONF;                              // 中央设备配置参数
  ST_CTL_CONN_INFO CTL_CONN[CONFIG_BT_MAX_CONN]; // 中央设备连接信息参数 PPL的值也占1
  ST_CTL_FUNC_CB CTL_FUNC;                              // 中央设备的函数
  ST_CTL_FLASH CTL_FLASH;                            // 中央设备保存连接过的设备信息参数

  ST_PPL_CONN_INFO PPL_CONN;  // 外围设备连接信息参数
  ST_PPL_FUNC_CB   PPL_FUNC;  // 外围设备回调函数

} ST_BLE_IINFO;
extern ST_BLE_IINFO BLE_INFO;

extern ST_Ble_MACList BLE_List_BlackList;//黑名单列表

#endif /*CENTRAL_AND_PERIPHERAL_H_*/
