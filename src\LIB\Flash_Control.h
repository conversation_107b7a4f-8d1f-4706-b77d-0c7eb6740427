/**
 * @file Flash_Control.h
 * @brief flash控制
 *
 * 该文件实现了片内FLASH的增、删、改、查功能。
 * 该功能是参考NRF的ZMS做的封装。官方对nRF54L建议使用Zephyr Memory Storage（ZMS）介绍：
 * https://docs.nordicsemi.com/bundle/ncs-latest/page/nrf/app_dev/device_guides/nrf54l/zms.html
 *
 * 官方的ZMS介绍：
 * https://docs.nordicsemi.com/bundle/ncs-latest/page/zephyr/services/storage/zms/zms.html
 *
 * @date 2025-06-12
 * <AUTHOR>
 */

#ifndef FLASH_CONTROL_H
#define FLASH_CONTROL_H

#include <stdbool.h>
#include <zephyr/fs/zms.h>
#include <zephyr/kernel.h>
#include <string.h>
#include <zephyr/device.h>
#include <zephyr/drivers/flash.h>
#include <zephyr/storage/flash_map.h>
#include <zephyr/sys/reboot.h>

#define ZMS_PARTITION user_storage /* 数据存储在flash的storage分区 */
#define ZMS_PARTITION_DEVICE FIXED_PARTITION_DEVICE(ZMS_PARTITION)
#define ZMS_PARTITION_OFFSET FIXED_PARTITION_OFFSET(ZMS_PARTITION)

/**
 * @brief ZMS 存储管理器结构体
 */
typedef struct
{
  struct zms_fs fs; /**< ZMS 文件系统实例 */
  bool is_mounted;        /**< 文件系统挂载状态 */
} Flash_Control_manager_t;

int Flash_Control_init(Flash_Control_manager_t *manager, const struct device *partition_device, off_t partition_offset, uint8_t sector_count);

int Flash_Control_mount(Flash_Control_manager_t *manager);

int Flash_Control_write(Flash_Control_manager_t *manager, uint32_t id, const void *data, size_t size);

int Flash_Control_read(Flash_Control_manager_t *manager, uint32_t id, void *data, size_t size);

int Flash_Control_delete(Flash_Control_manager_t *manager, uint32_t id);

int Flash_Control_get_state(Flash_Control_manager_t *manager, uint32_t id);

int Flash_Control_free_space(Flash_Control_manager_t *manager);

int Flash_Control_clear(Flash_Control_manager_t *manager);


int Flash_Control_gc(Flash_Control_manager_t *manager);

int Flash_Control_Test(void);
#endif  // FLASH_CONTROL_H