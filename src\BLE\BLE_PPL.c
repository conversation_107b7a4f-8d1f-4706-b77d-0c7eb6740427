/**
 * @file BLE_PPL.c
 * @brief 蓝牙外围设备模式
 *
 * 该文件实现了蓝牙外围设备模式下进行广播和连接
 * 包含：广播数据、等待BLE连接、数据发送。
 *
 * @date 2025-05-27
 * <AUTHOR>
 */
#include <zephyr/logging/log.h>

#include "System_Config.h"
#include "cell_cmd_parse.h"
#include "central_and_peripheral.h"
#include <zephyr/kernel.h>


#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(PPL, LOG_LEVEL_DBG);


/// @brief 外围设备GATT读写回调函数
/// @param write_cb 写入回调函数
/// @param read_cb 读取回调函数 
void BLE_PPL_GATT_FUNC_CB_Init(BLE_PPL_Write_callback_t write_cb, BLE_PPL_Read_callback_t read_cb)
{
  // 初始化外围设备回调函数
  BLE_INFO.PPL_FUNC.Write_CB  = write_cb;   // 设置写入回调函数
  BLE_INFO.PPL_FUNC.Read_CB   = read_cb;    // 设置读取回调函数
}

//--------------------------------------------------广播包数据定义--------------------------------------------------------------
static const struct bt_data ad[] = {
    // 定义蓝牙广播数据结构数组
    BT_DATA_BYTES(BT_DATA_FLAGS, (BT_LE_AD_LIMITED | BT_LE_AD_NO_BREDR)),  // 设置广播标志:通用可发现模式和仅支持LE
};

// static  struct bt_data sd_name[] = 
// {
//   BT_DATA_BYTES(BT_DATA_NAME_COMPLETE, DGLAB_DEVICE_NAME_APP),  // 设置完整设备名称
//   // BT_DATA_BYTES(BT_DATA_NAME_COMPLETE, CONFIG_BT_DEVICE_NAME,sizeof(CONFIG_BT_DEVICE_NAME) - 1),  // 设置完整设备名称
//   BT_DATA(BT_DATA_MANUFACTURER_DATA,&BLE_INFO.PPL_CONN.ADV.MANUFACTURER_DATA,BLE_INFO.PPL_CONN.ADV.MANUFACTURER_DATA_LEN), // 设置制造商数据
// };

// static  struct bt_data sd[] = 
// {
//   BT_DATA(BT_DATA_MANUFACTURER_DATA,&BLE_INFO.PPL_CONN.ADV.MANUFACTURER_DATA,10) // 设置制造商数据
// };

void adv_work_handler(struct k_work *work) // 开始广播的函数
{
  int err;
  if(BLE_INFO.PPL_CONN.G_Status_BLE.flag_usePublicAdv != true)//公共广播 //测试！！！！！！

  {
    struct bt_data sd_data[]=
    {
      {
      .type = BT_DATA_NAME_COMPLETE, // 设置完整设备名称
      .data = (const uint8_t *)DGLAB_DEVICE_NAME_APP, // 使用预定义的设备名称
      .data_len = sizeof(DGLAB_DEVICE_NAME_APP) - 1, // 设备
      },
      {
        .type = BT_DATA_MANUFACTURER_DATA, // 设置制造商数据
        .data = BLE_INFO.PPL_CONN.ADV.MANUFACTURER_DATA, // 使用预定义的制造商数据
        .data_len = BLE_INFO.PPL_CONN.ADV.MANUFACTURER_DATA_LEN, // 制造商数据的长度
      }
    };
    err = bt_le_adv_start(BT_LE_ADV_CONN_FAST_2, ad, ARRAY_SIZE(ad),(const struct bt_data *)sd_data,ARRAY_SIZE(sd_data)); // 启动快速广播
  }
  else//匿名广播
  {
    struct bt_data sd_data[]=
    {
      {
        .type = BT_DATA_MANUFACTURER_DATA, // 设置制造商数据
        .data = BLE_INFO.PPL_CONN.ADV.MANUFACTURER_DATA, // 使用预定义的制造商数据
        .data_len = BLE_INFO.PPL_CONN.ADV.MANUFACTURER_DATA_LEN, // 制造商数据的长度
      }
    };
    err = bt_le_adv_start(BT_LE_ADV_CONN_FAST_2, ad, ARRAY_SIZE(ad),(const struct bt_data *)sd_data,ARRAY_SIZE(sd_data)); // 启动快速广播
  }
  if (err) 
  {// 检查错误
    LOG_DBG("Advertising failed to start (err %d)\n", err); // 打印启动失败信息
    return;
  }
  LOG_INF("Advertising started\n"); // 打印广播启动成功信息
}

/**
 * @description: 启动广播工作队列
 * @return {*}
 */
void advertising_start(void) { k_work_submit(&BLE_INFO.PPL_CONN.adv_work); }



#if defined(CONFIG_BT_USER_PHY_UPDATE)
void update_phy(struct bt_conn *conn) {
  int err;

  // update PHY
  err = bt_conn_le_phy_update(conn, BT_CONN_LE_PHY_PARAM_2M);
  if (err) {
    LOG_DBG("PHY update failed (err %d)\n", err);
  } else {
    LOG_DBG("PHY update pending\n");
  }
}
#endif /* CONFIG_BT_USER_PHY_UPDATE */
static void exchange_func(struct bt_conn *conn, uint8_t att_err,
                          struct bt_gatt_exchange_params *params) {
  LOG_DBG("MTU exchange %s\r\n", att_err == 0 ? "successful" : "failed");
  if (!att_err)
  {
    uint16_t payload_mtu = bt_gatt_get_mtu(conn) - 3;  // 3 bytes used for Attribute headers.
    LOG_DBG("New MTU: %d bytes", payload_mtu);
  }
}

void update_mtu(struct bt_conn *conn) {
  int err;

  BLE_INFO.PPL_CONN.exchange_params.func = exchange_func;

  // update MTU
  err = bt_gatt_exchange_mtu(conn, &BLE_INFO.PPL_CONN.exchange_params);
  if (err) {
    LOG_DBG("MTU exchange failed (err %d)\n", err);
  } else {
    LOG_DBG("MTU exchange pending\n");
  }
}

//----------------------------------------OMS GATT服务表-------------------------------------------------------
static ssize_t BLE_PPL_OMS_Write_CB(struct bt_conn *conn, const struct bt_gatt_attr *attr, const void *buf, uint16_t len, uint16_t offset, uint8_t flags);
static ssize_t BLE_PPL_OMS_Read_CB(struct bt_conn *conn, const struct bt_gatt_attr *attr, void *buf,uint16_t len, uint16_t offset);
static void BLE_PPL_OMS_CCC_CFG_Changed_CB(const struct bt_gatt_attr *attr, uint16_t value);

static struct bt_gatt_attr MOS_Service_Attrs[] =
{
        //---------------------服务UUID-----------------------------
        BT_GATT_PRIMARY_SERVICE(DGLAB_SVC_OMS_UUID),

        //---------------------特征UUID-----------------------------
        BT_GATT_CHARACTERISTIC(DGLAB_CHR_RX_UUID,
                               BT_GATT_CHRC_WRITE_WITHOUT_RESP,  // BT_GATT_CHRC_WRITE |

                              //  BT_GATT_PERM_READ_AUTHEN | BT_GATT_PERM_WRITE_AUTHEN,
                              BT_GATT_PERM_WRITE,//非加密
                               NULL, BLE_PPL_OMS_Write_CB, NULL),
        //---------------------特征UUID-----------------------------
        BT_GATT_CHARACTERISTIC(DGLAB_CHR_TX_UUID, 
                              BT_GATT_CHRC_READ | BT_GATT_CHRC_NOTIFY,
                               BT_GATT_PERM_READ_AUTHEN,//加密
                              //  BT_GATT_PERM_READ,
                               BLE_PPL_OMS_Read_CB, NULL, NULL),
        BT_GATT_CCC(BLE_PPL_OMS_CCC_CFG_Changed_CB,
#ifdef CONFIG_BT_mos_AUTHEN
                    BT_GATT_PERM_READ_AUTHEN | BT_GATT_PERM_WRITE_AUTHEN),
#else
                    BT_GATT_PERM_READ | BT_GATT_PERM_WRITE),
#endif /* CONFIG_BT_mos_AUTHEN */

        
        // );
};

/// @brief  接收写回调函数
static ssize_t BLE_PPL_OMS_Write_CB(struct bt_conn *conn, const struct bt_gatt_attr *attr, const void *buf, uint16_t len, uint16_t offset, uint8_t flags)
{
  const uint8_t *const data = buf;
  // LOG_DBG("Received data, handle %d, conn %p", attr->handle, (void *)conn);
  char addr[BT_ADDR_LE_STR_LEN] = {0};
  bt_addr_le_to_str(bt_conn_get_dst(conn), addr, ARRAY_SIZE(addr));
  LOG_HEXDUMP_DBG( data, len, "Received data");
  if(BLE_INFO.PPL_FUNC.Write_CB != NULL)
  {
    // 调用外围设备写入回调函数
    BLE_INFO.PPL_FUNC.Write_CB(ePPL_CB_TYPE_Write_OMS_RX, buf,&len);
  }
  else
  {
    LOG_WRN("No OMS write callback set");
  }
  return len;
}

/// @brief  读取回调函数
static ssize_t BLE_PPL_OMS_Read_CB(struct bt_conn * conn, const struct bt_gatt_attr *attr, void *buf, uint16_t len, uint16_t offset)
{
    LOG_DBG("Read request, handle %d, conn %p", attr->handle, (void *)conn);
    char addr[BT_ADDR_LE_STR_LEN] = {0};
    bt_addr_le_to_str(bt_conn_get_dst(conn), addr, ARRAY_SIZE(addr));
    LOG_INF("Read request from: %s", addr);

    // 检查连接的安全级别
    struct bt_conn_info info;
    int err = bt_conn_get_info(conn, &info);
    if (err)
    {
      LOG_ERR("Failed to get conn info (err %d)", err);
      return BT_GATT_ERR(BT_ATT_ERR_AUTHENTICATION);
    }
    // LOG_DBG("Connection security level: %d", info.security.level);
    if (BLE_INFO.PPL_FUNC.Read_CB == NULL) // 检查回调函数是否为NULL
    {
      LOG_ERR("Read_CB must not be NULL");
      return BT_GATT_ERR(BT_ATT_ERR_UNLIKELY);
    }
    if (info.security.level < BT_SECURITY_L2)//无加密传输
    {
      LOG_WRN("Unauthenticated read request, providing limited data");
      return BLE_INFO.PPL_FUNC.Read_CB(ePPL_CB_TYPE_Read_OMS_TX, conn, attr, buf, &len, &offset);
    }
    else//加密状态下传输
    {
      return BLE_INFO.PPL_FUNC.Read_CB(ePPL_CB_TYPE_Read_OMS_TX_AUTHEN, conn, attr, buf, &len, &offset);
    }     
}

struct k_work_delayable test_work;
static void test_work_handler(struct k_work *work)
{
  size_t count = CONFIG_BT_ID_MAX;
  bt_addr_le_t ble_addr;
  uint8_t notify_data_buff[6]={0x53,BLE_Euipment_ID};
  LOG_INF("Test work handler executed");
  bt_id_get(&ble_addr, &count);// 获取所有已注册的蓝牙地址
  notify_data_buff[2] = ble_addr.a.val[3];
  notify_data_buff[3] = ble_addr.a.val[2];
  notify_data_buff[4] = ble_addr.a.val[1];
  notify_data_buff[5] = ble_addr.a.val[0];
  BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX,notify_data_buff,6);//响应头(0x53)_1, 配件ID_1, 蓝牙ADDR人类可读形式后4字节_4
 } 

/// @brief  CCC服务设置改变回调
static void BLE_PPL_OMS_CCC_CFG_Changed_CB(const struct bt_gatt_attr *attr, uint16_t value)
{
  
  
  LOG_DBG(" %s", value == BT_GATT_CCC_NOTIFY ? "on" : "off");
  BLE_INFO.PPL_CONN.CCC_EN.GATT_OMS = (value == BT_GATT_CCC_NOTIFY ? BT_mos_SEND_STATUS_ENABLED : BT_mos_SEND_STATUS_DISABLED);
  
   k_work_schedule(&test_work, K_MSEC(10));
}





//----------------------------------------INFO GATT服务表-------------------------------------------------------
static ssize_t BLE_PPL_INFO_Read_CB(struct bt_conn *conn, const struct bt_gatt_attr *attr, void *buf,uint16_t len, uint16_t offset);
static void BLE_PPL_INFO_CCC_CFG_Changed_CB(const struct bt_gatt_attr *attr, uint16_t value);
// BT_GATT_SERVICE_DEFINE(INFO_Service,
static struct bt_gatt_attr INFO_Service_Attrs[] =
{
        //---------------------服务UUID-----------------------------
        BT_GATT_PRIMARY_SERVICE(DGLAB_SVC_INFO_UUID),

        //---------------------特征UUID-----------------------------
        BT_GATT_CHARACTERISTIC(DGLAB_CHR_HWCD_UUID,
                               BT_GATT_CHRC_READ,  // BT_GATT_CHRC_WRITE |
                              //  BT_GATT_PERM_READ_AUTHEN | BT_GATT_PERM_WRITE_AUTHEN,
                               BT_GATT_PERM_READ,
                               BLE_PPL_INFO_Read_CB,NULL, NULL),

        //---------------------特征UUID-----------------------------
        BT_GATT_CHARACTERISTIC(DGLAB_CHR_HENV_UUID, 
                               BT_GATT_CHRC_READ | BT_GATT_CHRC_NOTIFY,
                              //  BT_GATT_PERM_READ_AUTHEN,
                               BT_GATT_PERM_READ,
                               BLE_PPL_INFO_Read_CB, NULL, NULL),
        BT_GATT_CCC(BLE_PPL_INFO_CCC_CFG_Changed_CB,
                    // BT_GATT_PERM_READ_AUTHEN | BT_GATT_PERM_WRITE_AUTHEN),
                    BT_GATT_PERM_READ | BT_GATT_PERM_WRITE),
        // );
};

/// @brief  读取回调函数
static ssize_t BLE_PPL_INFO_Read_CB(struct bt_conn * conn, const struct bt_gatt_attr *attr, void *buf, uint16_t len, uint16_t offset)
{
    LOG_DBG("Read request, handle %d, conn %p", attr->handle, (void *)conn);
    char addr[BT_ADDR_LE_STR_LEN] = {0};
    bt_addr_le_to_str(bt_conn_get_dst(conn), addr, ARRAY_SIZE(addr));
    LOG_INF("Read request from: %s", addr);
    // 检查连接的安全级别
    struct bt_conn_info info;
    int err = bt_conn_get_info(conn, &info);
    if (err) {
        LOG_ERR("Failed to get conn info (err %d)", err);
        return BT_GATT_ERR(BT_ATT_ERR_AUTHENTICATION);
    }
    if (BLE_INFO.PPL_FUNC.Read_CB == NULL) // 检查回调函数是否为NULL
    {
      LOG_ERR("Read_CB must not be NULL");
      return BT_GATT_ERR(BT_ATT_ERR_UNLIKELY);
    }

    // 判断是哪个特征的读请求
    if (bt_uuid_cmp(attr->uuid, DGLAB_CHR_HWCD_UUID) == 0)
    {
      // const char *limited_value = "INFO HWCD";
      return BLE_INFO.PPL_FUNC.Read_CB(ePPL_CB_TYPE_Read_INFO_HWCD, conn, attr, buf, &len, &offset);
    }
    else if (bt_uuid_cmp(attr->uuid, DGLAB_CHR_HENV_UUID) == 0)
    {
      // const char *limited_value = "INFO HENV";
      return BLE_INFO.PPL_FUNC.Read_CB(ePPL_CB_TYPE_Read_INFO_HENV, conn, attr, buf, &len, &offset);
    }
    return BT_GATT_ERR(BT_ATT_ERR_READ_NOT_PERMITTED);
}

/// @brief  CCC服务设置改变回调
static void BLE_PPL_INFO_CCC_CFG_Changed_CB(const struct bt_gatt_attr *attr, uint16_t value)
{
  LOG_INF("Notification has been turned %s", value == BT_GATT_CCC_NOTIFY ? "on" : "off");
  BLE_INFO.PPL_CONN.CCC_EN.GATT_INFO = (value == BT_GATT_CCC_NOTIFY ? BT_mos_SEND_STATUS_ENABLED : BT_mos_SEND_STATUS_DISABLED);
}



//----------------------------------------OTA GATT服务表-------------------------------------------------------
static void BLE_PPL_OTA_CCC_CFG_Changed_CB(const struct bt_gatt_attr *attr, uint16_t value);
static ssize_t BLE_PPL_OTA_Write_CB(struct bt_conn *conn, const struct bt_gatt_attr *attr, const void *buf, uint16_t len, uint16_t offset, uint8_t flags);
// BT_GATT_SERVICE_DEFINE(OTA_Service,
static struct bt_gatt_attr OTA_Service_Attrs[] =
{
        //---------------------服务UUID-----------------------------
        BT_GATT_PRIMARY_SERVICE(DGLAB_SVC_OTA_UUID),

        //---------------------特征UUID-----------------------------
        BT_GATT_CHARACTERISTIC(DGLAB_CHR_SYNC_UUID, 
                               BT_GATT_CHRC_NOTIFY | BT_GATT_CHRC_WRITE,
                               BT_GATT_PERM_WRITE,
                               NULL, BLE_PPL_OTA_Write_CB, NULL),
        BT_GATT_CCC(BLE_PPL_OTA_CCC_CFG_Changed_CB,
                    BT_GATT_PERM_READ | BT_GATT_PERM_WRITE),

        //---------------------特征UUID-----------------------------
        BT_GATT_CHARACTERISTIC(DGLAB_CHR_DATA_UUID,
                               BT_GATT_CHRC_WRITE_WITHOUT_RESP, 
                               BT_GATT_PERM_WRITE,
                               NULL, BLE_PPL_OTA_Write_CB, NULL),
        // );
};

/// @brief  CCC服务设置改变回调
static void BLE_PPL_OTA_CCC_CFG_Changed_CB(const struct bt_gatt_attr *attr, uint16_t value)
{
  LOG_INF("Notification has been turned %s", value == BT_GATT_CCC_NOTIFY ? "on" : "off");
  BLE_INFO.PPL_CONN.CCC_EN.GATT_OTA = (value == BT_GATT_CCC_NOTIFY ? BT_mos_SEND_STATUS_ENABLED : BT_mos_SEND_STATUS_DISABLED);
}


/// @brief  接收写回调函数
static ssize_t BLE_PPL_OTA_Write_CB(struct bt_conn *conn, const struct bt_gatt_attr *attr, const void *buf, uint16_t len, uint16_t offset, uint8_t flags)
{
  const uint8_t *const data = buf;
  LOG_DBG("Received data, handle %d, conn %p", attr->handle, (void *)conn);
  char addr[BT_ADDR_LE_STR_LEN] = {0};
  bt_addr_le_to_str(bt_conn_get_dst(conn), addr, ARRAY_SIZE(addr));
  LOG_HEXDUMP_DBG(data, len,"Rx;");

  if (bt_uuid_cmp(attr->uuid, DGLAB_CHR_DATA_UUID) == 0)
  {
    if (BLE_INFO.PPL_FUNC.Write_CB != NULL)
    {
      // 调用外围设备写入回调函数
      BLE_INFO.PPL_FUNC.Write_CB(ePPL_CB_TYPE_Write_OTA_DATA, buf, &len);
    }
    else
    {
      LOG_WRN("No OTA DATA write callback set");
    }
  }
  else if (bt_uuid_cmp(attr->uuid, DGLAB_CHR_SYNC_UUID) == 0)
  {
    if (BLE_INFO.PPL_FUNC.Write_CB != NULL)
    {
      // 调用外围设备写入回调函数
      BLE_INFO.PPL_FUNC.Write_CB(ePPL_CB_TYPE_Write_OTA_SYNC, buf, &len);
    }
    else
    {
      LOG_WRN("No OTA SYNC write callback set");
    }
  }

  // BLE_PPL_OTA_Send(conn, data, len);  // 测试：收什么发什么回去 发送数据到外围设备

  return len;
}



//-------------------------------GATT SETVER INIT---------------------------------
// 创建服务结构体
static struct bt_gatt_service MOS_Service = {
    .attrs = MOS_Service_Attrs,
    .attr_count = ARRAY_SIZE(MOS_Service_Attrs),
};

static struct bt_gatt_service INFO_Service = {
    .attrs = INFO_Service_Attrs,
    .attr_count = ARRAY_SIZE(INFO_Service_Attrs),
};

static struct bt_gatt_service OTA_Service = {
    .attrs = OTA_Service_Attrs,
    .attr_count = ARRAY_SIZE(OTA_Service_Attrs),
};

/**
 * @brief  初始化外围设备的GATT服务
*/
void BLE_PPL_Gatt_Services_Init(void)
{
  int err;
  k_work_init_delayable(&test_work, test_work_handler);
  // 先注册INFO服务
  err = bt_gatt_service_register(&INFO_Service);
  if (err)
  {
    LOG_ERR("Failed to register INFO service (err %d)", err);
  }
  else
  {
    LOG_DBG("INFO service registered successfully");
  }

  // 再注册MOS服务
  err = bt_gatt_service_register(&MOS_Service);
  if (err)
  {
    LOG_ERR("Failed to register MOS service (err %d)", err);
  }
  else
  {
    LOG_DBG("MOS service registered successfully");
  }

  // 再注册OTA服务
  err = bt_gatt_service_register(&OTA_Service);
  if (err)
  {
    LOG_ERR("Failed to register MOS service (err %d)", err);
  }
  else
  {
    LOG_DBG("MOS service registered successfully");
  }
}

/**
 * @brief  删除外围设备的GATT服务
 * @param *_service 需要删除的服务
 * @note    OTA的时候可以把其它传输功能的服务删除
 * @warning 删除服务时需要确保没有设备连接，否则可能会导致设备异常。需要重新初始化服务才能使用
*/
void BLE_PPL_Gatt_Services_Delete(struct bt_gatt_service *_service)
{
    bt_gatt_service_unregister(_service);
}

// /// @brief  发送数据到外围设备回调
// static void BLE_PPL_OMS_Send_cb(struct bt_conn *conn, void *user_data)
// {
//   LOG_DBG("Data send, conn %p", (void *)conn);
// }
// /// @brief  发送数据到外围设备
// int BLE_PPL_MOS_Send(struct bt_conn *conn, const uint8_t *data, uint16_t len)
// {
//   struct bt_gatt_notify_params params = {0};
//   const struct bt_gatt_attr *attr = &MOS_Service_Attrs[3];

//   params.attr = attr;
//   params.data = data;
//   params.len = len;
//   params.func = BLE_PPL_OMS_Send_cb;

//   if (!conn)
//   {
//     LOG_DBG("Notification send to all connected peers");
//     return bt_gatt_notify_cb(NULL, &params);
//   }
//   else if (bt_gatt_is_subscribed(conn, attr, BT_GATT_CCC_NOTIFY))
//   {
//     return bt_gatt_notify_cb(conn, &params);
//   }
//   else
//   {
//     return -EINVAL;
//   }
// }

// /// @brief  发送数据到外围设备回调
// static void BLE_PPL_INFO_Send_cb(struct bt_conn *conn, void *user_data)
// {
//   LOG_DBG("Data send, conn %p", (void *)conn);
// }
// /// @brief  发送数据到外围设备
// int BLE_PPL_INFO_Send(struct bt_conn *conn, const uint8_t *data, uint16_t len)
// {
//   struct bt_gatt_notify_params params = {0};
//   const struct bt_gatt_attr *attr = &INFO_Service_Attrs[3];

//   params.attr = attr;
//   params.data = data;
//   params.len = len;
//   params.func = BLE_PPL_INFO_Send_cb;

//   if (!conn)
//   {
//     LOG_DBG("Notification send to all connected peers");
//     return bt_gatt_notify_cb(NULL, &params);
//   }
//   else if (bt_gatt_is_subscribed(conn, attr, BT_GATT_CCC_NOTIFY))
//   {
//     return bt_gatt_notify_cb(conn, &params);
//   }
//   else
//   {
//     return -EINVAL;
//   }
// }

// /// @brief  发送数据到外围设备回调
// static void BLE_PPL_OTA_Send_cb(struct bt_conn *conn, void *user_data)
// {
//   LOG_DBG("Data send, conn %p", (void *)conn);
// }
// /// @brief  发送数据到外围设备
// int BLE_PPL_OTA_Send(struct bt_conn *conn, const uint8_t *data, uint16_t len)
// {
//   struct bt_gatt_notify_params params = {0};
//   const struct bt_gatt_attr *attr = &OTA_Service_Attrs[2];

//   params.attr = attr;
//   params.data = data;
//   params.len = len;
//   params.func = BLE_PPL_OTA_Send_cb;

//   if (!conn)
//   {
//     LOG_DBG("Notification send to all connected peers");
//     return bt_gatt_notify_cb(NULL, &params);
//   }
//   else if (bt_gatt_is_subscribed(conn, attr, BT_GATT_CCC_NOTIFY))
//   {
//     return bt_gatt_notify_cb(conn, &params);
//   }
//   else
//   {
//     return -EINVAL;
//   }
// }

/**************************************************************
 * @brief 发送数据到外围设备
 *
 * @param  _type 参考：ePPL_CB_Type_t
 * @param *data 发送的数据
 * @param len 发送的数据长度
 *
 * @return  参考：errno.h
 ***************************************************************/
int BLE_PPL_Write(ePPL_CB_Type_t _type, const uint8_t *data, uint16_t len)
{
  struct bt_gatt_notify_params params = {0};
  struct bt_gatt_attr *attr=NULL; 
  (_type == ePPL_CB_TYPE_Notify_OMS_TX)      ? attr = &MOS_Service_Attrs[3]
  : (_type == ePPL_CB_TYPE_Notify_INFO_HENV) ? attr = &INFO_Service_Attrs[3]
  : (_type == ePPL_CB_TYPE_Notify_OTA_SYNC)  ? attr = &OTA_Service_Attrs[2]
                                             : NULL;
  if (attr == NULL) //参数不对
  {
    LOG_ERR("Invalid notification type");
    return -EINVAL;
  }
  params.attr = attr;
  params.data = data;
  params.len = len;//strlen((const uint8_t *)data);
  params.func = NULL;
  if (!BLE_INFO.PPL_CONN.CONN)
  {
    LOG_DBG("Notification send to all connected peers");
    // return bt_gatt_notify_cb(NULL, &params);//发给所有连接的设备
    return -EINVAL;
  }
  if (bt_gatt_is_subscribed(BLE_INFO.PPL_CONN.CONN, params.attr, BT_GATT_CCC_NOTIFY))
  {
    return bt_gatt_notify_cb(BLE_INFO.PPL_CONN.CONN, &params);
  }
  else
  {
    return -EINVAL;
  }
}
