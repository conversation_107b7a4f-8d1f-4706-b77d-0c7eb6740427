/*
 * Copyright (c) 2021 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#include <stdint.h>
#include <zephyr/types.h>
#include "System_Config.h"
#include <zephyr/settings/settings.h>
#include <zephyr/drivers/watchdog.h>

#include "BLE_MACList.h"
#include "Flash_Control.h"
#include "Game_Common.h"
#include "UART_COM.h"
#include "GPIO_CONF.h"

#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(MAIN,  LOG_LEVEL_DBG);

Flash_Control_manager_t DG_LAB_FLASH;


appTL_t GLOBAL_APP_DATA;
int central_peripheral_init(void);

void peripheral_start(void); // 外设启动函数

//---------------------------------看门狗----------------------------
static const struct device *wdt = DEVICE_DT_GET(DT_ALIAS(watchdog0));
static struct wdt_timeout_cfg wdt_config;
/// @brief  看门狗回调函数
static void wdt_callback(const struct device *wdt_dev, int channel_id)
{
    LOG_ERR("Watchdog timer expired!");
    /* Perform any emergency actions here */
}
/// @brief  看门狗初始化函数
static int watchdog_init(void)
{
    int err;

    if (!device_is_ready(wdt))
    {
      LOG_ERR("Watchdog device not ready");
      return -ENODEV;
    }

    wdt_config.flags      = WDT_FLAG_RESET_SOC;
    wdt_config.window.min = 0;
    wdt_config.window.max = 2000;  // 2秒
    wdt_config.callback   = wdt_callback;

    err = wdt_install_timeout(wdt, &wdt_config);
    if (err < 0)
    {
      LOG_ERR("Watchdog install error");
      return err;
    }

    err = wdt_setup(wdt, WDT_OPT_PAUSE_HALTED_BY_DBG);
    if (err < 0)
    {
      LOG_ERR("Watchdog setup error");
      return err;
    }

    return 0;
}


int main(void)
{
	int rc;
	watchdog_init();//看门狗初始化
	// settings_subsys_init(); // 初始化设置子系统 貌似不用初始化 在蓝牙初始化时内部会自己启动初始化程序
	//-------------做一些测试------------------------
	// Flash_Control_Test();//测试OK 片内FLASH操作
	// BLE_MACList_Test();//测试OK	链表记录数据
	//-------------做一些测试 END------------------------

	
  rc = Flash_Control_init(&DG_LAB_FLASH, ZMS_PARTITION_DEVICE, ZMS_PARTITION_OFFSET, 3);// 初始化Flash管理器
  if (rc)
  {
    LOG_ERR("Flash manager init failed, rc=%d\n", rc);
    return rc;
  }
  UART_COM_Init();//初始化串口
	//  (void)init_central(CONFIG_BT_MAX_CONN, CONFIG_SAMPLE_CONN_ITERATIONS);
	central_peripheral_init();
	// peripheral_start();
	Game_Common_Init();
	GPIO_CONF_Init();//初始化GPIO
	for(;;)
  {
		static uint8_t time_count_1000mS=0;
		//1秒执行一次
		time_count_1000mS++;
		if(time_count_1000mS >= 10)
		{
			time_count_1000mS=0;
			wdt_feed(wdt, 0);  // 喂狗
		}
    k_sleep(K_MSEC(100));
  }
  return 0;
}
