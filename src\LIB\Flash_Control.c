/**
 * @file Flash_Control.h
 * @brief flash控制
 *
 * 该文件实现了片内FLASH的增、删、改、查功能
 *
 * @date 2025-06-12
 * <AUTHOR>
 */

#include "Flash_Control.h"

#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(FLASH,  LOG_LEVEL_DBG);

/**
 * @brief 初始化 ZMS 存储管理器
 *
 * @param manager ZMS 管理器指针
 * @param partition_device 分区设备
 * @param partition_offset 分区偏移量
 * @param sector_count 扇区数量
 * @return int 0表示成功，负值表示错误码
 */
int Flash_Control_init(Flash_Control_manager_t *manager, const struct device *partition_device, off_t partition_offset, uint8_t sector_count)
{
  int rc;
  struct flash_pages_info info;

  if (!manager || !partition_device)
  {
    return -EINVAL;
  }

  if (!device_is_ready(partition_device))
  {
    return -ENODEV;
  }

  memset(manager, 0, sizeof(Flash_Control_manager_t));
  manager->fs.flash_device = partition_device;
  manager->fs.offset = partition_offset;

  // 获取 flash 页信息，主要是页大小
  rc = flash_get_page_info_by_offs(manager->fs.flash_device, manager->fs.offset, &info);
  if (rc)
  {
    return rc;
  }
  
  manager->fs.sector_size = info.size;  // 设置扇区大小
  manager->fs.sector_count = sector_count;
  manager->is_mounted = false;

  return 0;
}

/**
 * @brief 挂载 ZMS 文件系统
 *
 * @param manager ZMS 管理器指针
 * @return int 0表示成功，负值表示错误码
 */
int Flash_Control_mount(Flash_Control_manager_t *manager)
{
  int rc;

  if (!manager)
  {
    return -EINVAL;
  }

  if (manager->is_mounted)
  {
    return 0;  // 已经挂载
  }

  rc = zms_mount(&manager->fs);
  if (rc)
  {
    return rc;
  }

  manager->is_mounted = true;
  return 0;
}

/**
 * @brief 写入数据到指定 ID
 *
 * @param manager ZMS 管理器指针
 * @param id 数据 ID
 * @param data 数据指针
 * @param size 数据大小
 * @return int 写入的字节数，负值表示错误码
 */
int Flash_Control_write(Flash_Control_manager_t *manager, uint32_t id, const void *data, size_t size)
{
  int rc;

  if (!manager || !data)
  {
    return -EINVAL;
  }

  if (!manager->is_mounted)
  {
    rc = Flash_Control_mount(manager);
    if (rc)
    {
      return rc;
    }
  }

  return zms_write(&manager->fs, id, data, size);
}

/**
 * @brief 读取指定 ID 的数据
 *
 * @param manager ZMS 管理器指针
 * @param id 数据 ID
 * @param data 数据缓冲区
 * @param size 缓冲区大小
 * @return int 读取的字节数，0表示未找到，负值表示错误码
 */
int Flash_Control_read(Flash_Control_manager_t *manager, uint32_t id, void *data, size_t size)
{
  int rc;

  if (!manager || !data)
  {
    return -EINVAL;
  }

  if (!manager->is_mounted)
  {
    rc = Flash_Control_mount(manager);
    if (rc)
    {
      return rc;
    }
  }

  return zms_read(&manager->fs, id, data, size);
}

/**
 * @brief 删除指定 ID 的数据
 *
 * @param manager ZMS 管理器指针
 * @param id 数据 ID
 * @return int 0表示成功，负值表示错误码
 */
int Flash_Control_delete(Flash_Control_manager_t *manager, uint32_t id)
{
  int rc;

  if (!manager)
  {
    return -EINVAL;
  }

  if (!manager->is_mounted)
  {
    rc = Flash_Control_mount(manager);
    if (rc)
    {
      return rc;
    }
  }

  return zms_delete(&manager->fs, id);
}

/**
 * @brief 检查指定 ID 的数据是否存在
 *
 * @param manager ZMS 管理器指针
 * @param id 数据 ID
 * @return int 数据长度，0表示未找到，负值表示错误码
 */
int Flash_Control_get_state(Flash_Control_manager_t *manager, uint32_t id)
{
  int rc;

  if (!manager)
  {
    return -EINVAL;
  }

  if (!manager->is_mounted)
  {
    rc = Flash_Control_mount(manager);
    if (rc)
    {
      return rc;
    }
  }

  return zms_get_data_length(&manager->fs, id);
}

/**
 * @brief 计算存储空间剩余大小
 *
 * @param manager ZMS 管理器指针
 * @return int 剩余字节数，负值表示错误码
 */
int Flash_Control_free_space(Flash_Control_manager_t *manager)
{
  int rc;

  if (!manager)
  {
    return -EINVAL;
  }

  if (!manager->is_mounted)
  {
    rc = Flash_Control_mount(manager);
    if (rc)
    {
      return rc;
    }
  }

  return zms_calc_free_space(&manager->fs);
}

/**
 * @brief 清空整个存储空间
 *
 * @param manager ZMS 管理器指针
 * @return int 0表示成功，负值表示错误码
 */
int Flash_Control_clear(Flash_Control_manager_t *manager)
{
  int rc;

  if (!manager)
  {
    return -EINVAL;
  }

  if (!manager->is_mounted)
  {
    rc = Flash_Control_mount(manager);
    if (rc)
    {
      return rc;
    }
  }

  return zms_clear(&manager->fs);
}

/**
 * @brief 执行垃圾回收
 *
 * @param manager ZMS 管理器指针
 * @return int 0表示成功，负值表示错误码
 */
int Flash_Control_gc(Flash_Control_manager_t *manager)
{
  int rc;
  uint8_t i;

  if (!manager)
  {
    return -EINVAL;
  }

  if (!manager->is_mounted)
  {
    rc = Flash_Control_mount(manager);
    if (rc)
    {
      return rc;
    }
  }
  // 对所有扇区进行垃圾回收
  for (i = 0; i < manager->fs.sector_count; i++)
  {
    rc = zms_sector_use_next(&manager->fs);
    if (rc)
    {
      return rc;
    }
  }

  return 0;
}

//----------------------------------TEST---------------------------------------------
#if 0
 Flash_Control_manager_t manager;
#define IP_ADDRESS_ID 1
#define MAC_ADDRESS_ID 2
int Flash_Control_Test(void)
{
  int rc;
  char buf[17];
  char new_mac[17] = "AA:BB:CC:DD:EE:00";
 

  // 初始化Flash管理器
  rc = Flash_Control_init(&manager, ZMS_PARTITION_DEVICE, ZMS_PARTITION_OFFSET, 3);
  if (rc)
  {
    LOG_ERR("Flash manager init failed, rc=%d\n", rc);
    return rc;
  }
  LOG_INF("Flash manager initialized successfully\n");

  // 1. 读取
  LOG_INF("\n--- 1. READ ---\n");
  rc = Flash_Control_read(&manager, IP_ADDRESS_ID, buf, sizeof(buf));
  if (rc == -ENOENT)  // 没有这个ID数据
  {
    LOG_INF("No data found for ID %u\n", IP_ADDRESS_ID);
    // 2. 创建：写入mac地址
    LOG_INF("\n--- 2. NEW Write ---\n");
    LOG_INF("Writing IP address %s to ID %u\n", new_mac, IP_ADDRESS_ID);
    rc = Flash_Control_write(&manager, IP_ADDRESS_ID, new_mac, strlen(new_mac));
    if (rc < 0)
    {
      LOG_ERR("Error while writing IP address, rc=%d\n", rc);
      return rc;
    }
    LOG_INF("Successfully wrote %d bytes\n", rc);
  }
  else if (rc <= 0)
  {
    LOG_ERR("Error or no data found while reading IP address, rc=%d\n", rc);
    return rc;
  }
  else //读到了数据
  {
    // 3. 修改：更新IP地址 把ID1写到ID2 并修改ID1数据+1
    LOG_INF("--- 3. Amend ---\n");
    Flash_Control_write(&manager, MAC_ADDRESS_ID, buf, strlen(buf));//把ID1写到ID2
    // 将IP地址字符串转换为整数
    sscanf(buf, "%u.%u.%u.%u", &buf[0], &buf[1], &buf[2], &buf[3]);
    new_mac[0] = (char)buf[0];
    new_mac[1] = (char)buf[1];
    new_mac[2] = (char)buf[2];
    new_mac[3] = (char)buf[3];
    // 最后一个数字加1
    new_mac[3]++;
    // 转回字符串
    snprintf(new_mac, sizeof(new_mac), "%u.%u.%u.%u", new_mac[0], new_mac[1], new_mac[2], new_mac[3]);
    // strcpy(new_mac, "*************");
    LOG_INF("Updating IP address to %s\n", new_mac);
    rc = Flash_Control_write(&manager, IP_ADDRESS_ID, new_mac, strlen(new_mac));
    if (rc < 0)
    {
      LOG_ERR("Error while updating IP address, rc=%d\n", rc);
      return rc;
    }
    LOG_INF("Successfully updated IP address, wrote %d bytes\n", rc);
  }
  
  // 验证修改结果
  memset(buf, 0, sizeof(buf));
  rc = Flash_Control_read(&manager, IP_ADDRESS_ID, buf, sizeof(buf));
  if (rc <= 0)
  {
    LOG_ERR("Error while reading updated IP address, rc=%d\n", rc);
    return rc;
  }
  buf[rc] = '\0';
  LOG_INF("Read updated IP address: %s (length: %d)\n", buf, rc);

  // 计算剩余空间
  rc = Flash_Control_free_space(&manager);
  if (rc < 0)
  {
    LOG_ERR("Error while calculating free space, rc=%d\n", rc);
    return rc;
  }
  LOG_INF("\nFree space in storage: %d bytes\n", rc);

  // 4. 删除：移除IP地址
  // printk("\n--- 4. Delete ---\n");
  // rc = Flash_Control_delete(&manager, IP_ADDRESS_ID);
  // if (rc) {
  //   printk("Error while deleting IP address, rc=%d\n", rc);
  //   return rc;
  // }
  // printk("Successfully deleted IP address\n");

  // 计算剩余空间
  // rc = Flash_Control_free_space(&manager);
  // if (rc < 0)
  // {
  //   LOG_ERR("Error while calculating free space, rc=%d\n", rc);
  //   return rc;
  // }
  // LOG_INF("\nFree space in storage: %d bytes\n", rc);
  // 清空所有flash
  
}
#endif
