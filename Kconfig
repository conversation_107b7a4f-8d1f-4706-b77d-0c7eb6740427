# Copyright (c) 2021 Nordic Semiconductor ASA
# SPDX-License-Identifier: Apache-2.0

mainmenu "Bluetooth: Central Multilink"

config SAMPLE_CONN_ITERATIONS
	int "Connection iterations"
	range 0 255
	default 0
	help
	  Number of times to connect and disconnect to peripheral_identity
	  sample.
	  
config DGLAB_LVGL_DISPLAY_ST7789
    bool "Enable ST7789 Display"
    default n
    help
      Enable ST7789 display driver.
      When enabled, CONFIG_SM_LVGL_DISPLAY_ST7789 will be defined as 1 in code.
      When disabled, CONFIG_SM_LVGL_DISPLAY_ST7789 will be defined as 0 in code.
    
config DGLAB_BLUETOOTH_PAIRING_CODE
  int "Custom Bluetooth pairing code"
  default 47124
  help
    Set the default custom Bluetooth pairing code.

source "Kconfig.zephyr"
