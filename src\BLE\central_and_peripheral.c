/**
 * @file central_and_peripheral.c
 * @brief 蓝牙中央和外围设备模式下的公共功能
 *
 * CTL:Central     中央设备
 * PPL:Peripheral  外围设备
 * 该文件实现了蓝牙中央和外围设备模式下的连接、断开连接、MTU交换等功能。
 *
 * @date 2025-05-27
 * <AUTHOR>
 */
#include "central_and_peripheral.h"

#include <zephyr/settings/settings.h>
#include "Game_Common.h"

#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(CAP, LOG_LEVEL_DBG);



// extern struct bt_conn *CTL_conn_connecting; /* 正在连接的蓝牙连接 */

ST_BLE_IINFO BLE_INFO = {
  .CTL_FLASH = 
  {
    .INFO_Array = { [0 ... (BLE_CTL_Device_Connect_MAX-1)] = { .Flash_ID_Num = 0 } }
  },
};

static void connected(struct bt_conn *conn, uint8_t reason);     // 连接建立时的回调函数
static void disconnected(struct bt_conn *conn, uint8_t reason);  // 断开连接时的回调函数


//--------------------------------------------------MTU--------------------------------------------------------------
#if defined(CONFIG_BT_GATT_CLIENT)
/* MTU交换完成后的回调函数 */
static void mtu_exchange_cb(struct bt_conn *conn, uint8_t err,
                            struct bt_gatt_exchange_params *params) {
  LOG_DBG("MTU exchange %u %s (%u)\n", bt_conn_index(conn),
          err == 0U ? "successful" : "failed", bt_gatt_get_mtu(conn));
}

/* 为每个连接创建MTU交换参数 */
static struct bt_gatt_exchange_params mtu_exchange_params[CONFIG_BT_MAX_CONN];

/* 执行MTU交换的函数 */
int mtu_exchange(struct bt_conn *conn) 
{
  int err;
  uint8_t index = bt_conn_index(conn);/* 获取连接索引 */

  /* 打印当前MTU值 */
  LOG_DBG("MTU (%u): %u\n", index, bt_gatt_get_mtu(conn));

  /* 设置MTU交换回调函数 */
  mtu_exchange_params[index].func = mtu_exchange_cb;

  /* 发起MTU交换请求 */
  err = bt_gatt_exchange_mtu(conn, &mtu_exchange_params[index]);
  if (err) {
    LOG_DBG("MTU exchange failed (err %d)", err);
  } else {
    LOG_DBG("Exchange pending...");
  }

  return err;
}
#endif /* CONFIG_BT_GATT_CLIENT */

/// @brief 连接建立时的回调函数
static void connected(struct bt_conn *conn, uint8_t reason)
{
  int err;
  struct bt_conn_info conn_info;
  // 获取连接信息
  err = bt_conn_get_info(conn, &conn_info);
  if (err)
  {
    LOG_ERR("---bt_conn_get_info---\r\n");
    return;  // 如果获取失败，直接返回
  }
  switch (conn_info.role)
  {
    case BT_CONN_ROLE_CENTRAL:  // 中央模式
    { 
      // uint8_t index = bt_conn_index(conn);//从0计
      uint8_t conf_index=bt_conn_index(conn);
      if(BLE_INFO.CTL_CONF.Index_MAX < conf_index)
      {
        BLE_INFO.CTL_CONF.Index_MAX = conf_index;//用于查连接MAC
      }
      char addr[BT_ADDR_LE_STR_LEN];
      // BLE_INFO.CTL_CONF.Index_MAP[index]=BLE_INFO.CTL_CONF.conn_count;
      // conf_index=BLE_INFO.CTL_CONF.Index_MAP[index];
      BLE_INFO.CTL_CONF.Index =conf_index;
      bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr));  // 将蓝牙地址转换为字符串形式
      if (reason)  // 连接失败
      {
        bt_conn_unref(BLE_INFO.CTL_CONF.CONN);  // 释放连接资源
        BLE_INFO.CTL_CONF.CONN=NULL;//释放
        // LOG_INF("Failed to connect to %s (%u)\n", addr, reason);  // 如果连接失败，打印错误信息
        for(uint8_t i=0;i<BLE_CTL_Device_Connect_MAX;i++)
        {
          if(memcmp(&BLE_INFO.CTL_CONF.OFFLINE.MAC[i],bt_conn_get_dst(conn),sizeof(BLE_INFO.CTL_CONF.OFFLINE.MAC[i])) == 0)//相同
          {
            if(BLE_INFO.CTL_CONF.OFFLINE.Count_Num_Max > 0)
            {
              (BLE_INFO.CTL_CONF.OFFLINE.Count_Num >= BLE_INFO.CTL_CONF.OFFLINE.Count_Num_Max - 1) ? (BLE_INFO.CTL_CONF.OFFLINE.Count_Num = 0) : (BLE_INFO.CTL_CONF.OFFLINE.Count_Num++);
            }
          }
        }
        // bt_conn_unref(CTL_conn_connecting);  // 释放连接资源
        // CTL_conn_connecting = NULL;
        // BLE_CTL_LE_start_scan_begin();  // 重新开始扫描
        return;
      }
      else  // 连接成功
      {
        BLE_INFO.CTL_CONF.CONN=NULL;//释放
        for(uint8_t i=0;i<BLE_CTL_Device_Connect_MAX;i++)
        {
          if(memcmp(&BLE_INFO.CTL_CONF.OFFLINE.MAC[i],bt_conn_get_dst(conn),sizeof(BLE_INFO.CTL_CONF.OFFLINE.MAC[i])) == 0)//相同
          {            
            BLE_INFO.CTL_CONF.OFFLINE.Await_Updata_Flag = true;
          }
        }
        if (BLE_INFO.CTL_CONN[conf_index].state == true)
        {
          LOG_ERR("conf_index error\n");
          return;
        }
        BLE_INFO.CTL_CONN[conf_index].state = true;
        BLE_INFO.CTL_CONN[conf_index].CONN = conn;
        BLE_INFO.CTL_CONN[conf_index].index = conf_index;  // 记录索引
        // LOG_INF("index:%d CONN index:%d\n",index,bt_conn_index( BLE_INFO.CTL_CONN[index].CONN));
      }
      
      // bt_hci_get_conn_handle(conn, &BLE_INFO.CTL_CONN[index].connhandle);    // 获取连接的连接句柄。
      // BLE_INFO.CTL_CONF.Validity_BitFlag |= (1<<conf_index);
      memcpy(&BLE_INFO.CTL_CONN[conf_index].mac, bt_conn_get_dst(conn), sizeof(bt_addr_le_t));  // 记录MAC
      // 打印连接成功信息
      LOG_DBG("Connected NUM:(%u): %s\n", BLE_INFO.CTL_CONF.conn_count, addr);
// #if defined(CONFIG_BT_SMP)
//       // 如果启用了安全管理协议，设置安全等级
//       err = bt_conn_set_security(conn, BT_SECURITY_L2);
//       if (err)
//       {
//         // 如果设置安全等级失败，打印错误信息
//         LOG_DBG("Failed to set security (%d).\n", err);
//       }
// #endif

#if defined(CONFIG_BT_GATT_CLIENT)
      // 如果启用了GATT客户端，执行MTU交换
      // mtu_exchange(conn);
#endif
      BLE_CLT_start_service_discovery(conn);  // 查询GATT服务
      if (err)
      {
        LOG_ERR("Discover failed0(err %d)\n", err);
      }
      // 增加连接计数
      if(BLE_INFO.CTL_CONF.conn_count < BLE_CTL_Device_Connect_MAX-1)
        BLE_INFO.CTL_CONF.conn_count++;
      // if (BLE_INFO.CTL_CONF.conn_count < BLE_INFO.CTL_CONF.conn_count_max)
      // {
      //   // 如果还未达到最大连接数，继续扫描
      //   BLE_CTL_LE_start_scan_begin();
      // }
      
      //启用写合法性验证
      BLE_MACList_Add_With_CTL_Index(&BLE_List_Validity, &BLE_INFO.CTL_CONN[conf_index].mac,conf_index);//添加到链表内
      // BLE_MACList_Add(&BLE_List_Validity, &BLE_INFO.CTL_CONN[conf_index].mac);
      // BLE_INFO.CTL_CONF.Validity_Chack[conf_index].Index = index;//索引记录
      // k_msgq_put(&Validity_Check_Queue,bt_conn_get_dst(conn), K_NO_WAIT);//放入队列内
      k_work_submit(&BLE_INFO.CTL_FUNC.validity_check_work);//开启安全性检测
      BLE_MACList_Remove(&BLE_List_Offline, bt_conn_get_dst(conn));//删除MAC
      // BLE_INFO.CTL_CONF.Auto_SCAN_Flag =  true;//更新一下自动扫描数据
    }
    break;
    case BT_CONN_ROLE_PERIPHERAL:  // 外围模式模式从机
    {
      char addr[BT_ADDR_LE_STR_LEN];  // 用于存储蓝牙地址的字符串
      // struct bt_conn_info info;
      LOG_DBG("PERIPHERAL MODE\n");
      BLE_INFO.PPL_CONN.CONN = bt_conn_ref(conn);  // 连接句柄
      // bt_conn_get_info(conn, &info);
      bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr));  // 将蓝牙地址转换为字符串形式
      if (err)
      {  // 如果连接过程中出现错误
        LOG_ERR("Failed to connect to %s 0x%02x %s\n", addr, err, bt_hci_err_to_str(err));  // 打印连接失败信息
        // adv_start(); // 重新开始广播
        advertising_start();
        return;  // 返回
      }
      #if defined(CONFIG_BT_USER_PHY_UPDATE)
      update_phy(conn);
      #endif /* CONFIG_BT_USER_PHY_UPDATE */
      // update_mtu(conn);
      LOG_DBG("Connected ppl: %s\n", addr);  // 打印连接成功信息
    }
    break;
    default:
      break;
  }
}

/// @brief 断开连接时的回调函数
static void disconnected(struct bt_conn *conn, uint8_t reason)
{
  int err;
  struct bt_conn_info conn_info;
  // 获取连接信息
  err = bt_conn_get_info(conn, &conn_info);
  if (err)
  {
    LOG_ERR("---bt_conn_get_info---\r\n");
    return;  // 如果获取失败，直接返回
  }
  uint8_t conn_index=bt_conn_index(conn);
  switch (conn_info.role)
  {
    case BT_CONN_ROLE_CENTRAL:  // 中央模式
    {
      char addr[BT_ADDR_LE_STR_LEN];
      // 将蓝牙地址转换为字符串形式
      bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr));
      // 打印断开连接的信息，包括地址、原因代码和原因描述
      LOG_DBG("CENTRAL Disconnected: %s, reason 0x%02x %s\n", addr, reason, bt_hci_err_to_str(reason));
      //---------------巡回犬模式下玩法终端------------------------------
      if (GAME_INFO.Game_Play.Hover_Dog.Start_EN == true && GAME_INFO.Game_Play.Hover_Dog.Mode_Start_Flag == true && GAME_INFO.Game_Play.Time_Over > 0)  // 使能 && 巡回犬玩法开启
      {
        int8_t conn_idex     = BLE_ATT_Get_CTL_CONN_Index_Fo_SubDevice(GAME_INFO.Game_Play.Hover_Dog.Triger_ID);//索引
        if(memcmp(&BLE_INFO.CTL_CONN[conn_idex].mac,bt_conn_get_dst(conn),sizeof(BLE_INFO.CTL_CONN[conn_idex].mac)) == 0)//断开的正好是触发的
        {
          GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type = eDisconnect;//设备断开
        }
      }
      //-----------停止合法性数据发送---------------
      BLE_MACList_Remove(&BLE_List_Validity, bt_conn_get_dst(conn));//删除MAC
      BLE_INFO.CTL_CONF.Validity_BitFlag &= ~(1<<conn_index);//清理
      BLE_INFO.CTL_CONF.Auto_SCAN_Flag =  true;//更新一下自动扫描数据
      // uint8_t conn_index=BLE_INFO.CTL_CONF.Index_MAP[bt_conn_index(conn)];
      memset(&BLE_INFO.CTL_CONN[conn_index], 0, sizeof(BLE_INFO.CTL_CONN[conn_index]));  // 清数据
      BLE_INFO.CTL_CONN[conn_index].state = false;
      // 释放连接引用
      bt_conn_unref(conn);
      // 减少连接计数
      LOG_DBG("CONN COUNT:%d",BLE_INFO.CTL_CONF.conn_count);
      if(BLE_INFO.CTL_CONF.conn_count>0)
        BLE_INFO.CTL_CONF.conn_count--;
      if (BLE_INFO.CTL_CONF.conn_count <BLE_CTL_Device_Connect_MAX && BLE_INFO.CTL_CONF.Disconn_Scan_EN_Flag == true)
      {
        BLE_INFO.CTL_CONF.Disconn_Scan_EN_Flag = false;// 关闭断开设备后的扫描 只扫一次
        // BLE_CTL_LE_start_scan_begin();
      }
      
    }
    break;
    case BT_CONN_ROLE_PERIPHERAL:  // 外围模式
    {
      char addr[BT_ADDR_LE_STR_LEN];  // 用于存储蓝牙地址的字符串

      bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr));  // 将蓝牙地址转换为字符串形式

      LOG_DBG("PERIPHERAL Disconnected: %s, reason 0x%02x %s\n", addr, reason, bt_hci_err_to_str(reason));  // 打印断开连接信息
      if (conn == BLE_INFO.PPL_CONN.CONN)
      {
        bt_conn_unref(BLE_INFO.PPL_CONN.CONN);
        BLE_INFO.PPL_CONN.CONN = NULL;
        advertising_start();
      }
    }
    break;
    default:
      break;
  }
}

/**
 * @brief 断开连接资源回收完成时的回调函数
 * 
 * @param conn 连接对象指针
 */
// static void recycled_cb(struct bt_conn *conn)
// {
  // char addr[BT_ADDR_LE_STR_LEN];
  // bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr));
  
  // struct bt_conn_info conn_info;
  // // 获取连接信息
  // bt_conn_get_info(conn, &conn_info);
  // switch (conn_info.role)
  // {
  //   case BT_CONN_ROLE_CENTRAL:  // 中央模式
  //   {
  //     LOG_DBG("CTL: %s\n", addr);
  //     BLE_INFO.CTL_CONF.Auto_SCAN_Flag =  true;//更新一下自动扫描数据
  //   }
  //   break;
  //   case BT_CONN_ROLE_PERIPHERAL:  // 外围模式
  //   {
  //     LOG_DBG("PPL: %s\n", addr);
  //   }
  //   break;
  //   default:
  //     break;
  // }
// }
/// @brief LE连接参数请求的回调函数
static bool le_param_req(struct bt_conn *conn, struct bt_le_conn_param *param)
{
  char addr[BT_ADDR_LE_STR_LEN];

  // 将蓝牙地址转换为字符串形式
  bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr));

  // 打印LE连接参数请求的信息
  LOG_DBG("LE conn param req: %s int (0x%04x, 0x%04x) lat %d to %d\n", addr, param->interval_min, param->interval_max, param->latency, param->timeout);

  // 返回true表示接受这个连接参数请求
  return true;
}

/// @brief LE连接参数更新时的回调函数
static void le_param_updated(struct bt_conn *conn, uint16_t interval, uint16_t latency, uint16_t timeout)
{
  char addr[BT_ADDR_LE_STR_LEN];

  // 将蓝牙地址转换为字符串形式
  bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr));

  // 打印更新后的LE连接参数信息
  LOG_DBG("LE conn param updated: %s int 0x%04x lat %d to %d\n", addr, interval, latency, timeout);
}

#if defined(CONFIG_BT_SMP)
static void security_changed(struct bt_conn *conn, bt_security_t level, enum bt_security_err err)
{
  char addr[BT_ADDR_LE_STR_LEN];

  bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr));

  if (!err)
  {
    LOG_DBG("Security changed: %s level %u\n", addr, level);
  }
  else
  {
    LOG_DBG("Security failed: %s level %u err %d %s\n", addr, level, err, bt_security_err_to_str(err));
  }
}
#endif

#if defined(CONFIG_BT_USER_PHY_UPDATE)
static void le_phy_updated(struct bt_conn *conn, struct bt_conn_le_phy_info *param)
{
  char addr[BT_ADDR_LE_STR_LEN];

  bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr));

  LOG_DBG("LE PHY Updated: %s Tx 0x%x, Rx 0x%x\n", addr, param->tx_phy, param->rx_phy);
}
#endif /* CONFIG_BT_USER_PHY_UPDATE */

#if defined(CONFIG_BT_USER_DATA_LEN_UPDATE)
static void le_data_len_updated(struct bt_conn *conn, struct bt_conn_le_data_len_info *info)
{
  char addr[BT_ADDR_LE_STR_LEN];

  bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr));

  LOG_DBG("Data length updated: %s max tx %u (%u us) max rx %u (%u us)\n", addr, info->tx_max_len, info->tx_max_time, info->rx_max_len, info->rx_max_time);
}
#endif /* CONFIG_BT_USER_DATA_LEN_UPDATE */

static struct bt_conn_cb conn_callbacks = {
    .connected = connected,  // 连接建立时的回调函数
    .disconnected = disconnected,  // 连接断开时的回调函数
    // .recycled         = recycled_cb,        //断开完成回调
    .le_param_req = le_param_req,  // LE连接参数请求的回调函数
    .le_param_updated = le_param_updated,  // LE连接参数更新时的回调函数

#if defined(CONFIG_BT_SMP)
    .security_changed = security_changed,  // 安全级别变更时的回调函数  
#endif

#if defined(CONFIG_BT_USER_PHY_UPDATE)
    .le_phy_updated = le_phy_updated,  // LE PHY更新时的回调函数
#endif /* CONFIG_BT_USER_PHY_UPDATE */

#if defined(CONFIG_BT_USER_DATA_LEN_UPDATE)
    .le_data_len_updated = le_data_len_updated,  // LE数据长度更新时的回调函数
#endif
    /* CONFIG_BT_USER_DATA_LEN_UPDATE */
};





// 显示配对密钥的回调函数
static void auth_passkey_display(struct bt_conn *conn, unsigned int passkey)
{
  char addr[BT_ADDR_LE_STR_LEN]; // 用于存储蓝牙地址的字符串

  bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr)); // 获取连接设备的蓝牙地址

  printk("Passkey for %s: %06u\n", addr, passkey); // 打印配对密钥
}

// 取消配对的回调函数
static void auth_cancel(struct bt_conn *conn)
{
  char addr[BT_ADDR_LE_STR_LEN]; // 用于存储蓝牙地址的字符串

  bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr)); // 获取连接设备的蓝牙地址

  printk("Pairing cancelled: %s\n", addr); // 打印取消配对信息
}

// pairing_confirm 回调函数：自动确认配对
static void pairing_confirm(struct bt_conn *conn)
{
    char addr[BT_ADDR_LE_STR_LEN];
    bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr));
    printk("Automatically confirming pairing for %s\n", addr);
    // 直接将固定配对码传递给蓝牙堆栈完成认证
    bt_conn_auth_passkey_entry(conn, CONFIG_DGLAB_BLUETOOTH_PAIRING_CODE);
}

// 配对认证回调结构体
static struct bt_conn_auth_cb conn_auth_callbacks = {
  .passkey_display = auth_passkey_display, // 配对密钥显示回调
  .cancel = auth_cancel,                   // 取消配对回调
  .pairing_confirm = pairing_confirm,  // 所有传入的配对请求都会调用这个回调
};



// 配对完成的回调函数
static void pairing_complete(struct bt_conn *conn, bool bonded)
{
  char addr[BT_ADDR_LE_STR_LEN]; // 用于存储蓝牙地址的字符串

  bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr)); // 获取连接设备的蓝牙地址

  printk("Pairing completed: %s, bonded: %d\n", addr, bonded); // 打印配对完成信息和是否已绑定
}

// 配对失败的回调函数
static void pairing_failed(struct bt_conn *conn, enum bt_security_err reason)
{
  char addr[BT_ADDR_LE_STR_LEN]; // 用于存储蓝牙地址的字符串

  bt_addr_le_to_str(bt_conn_get_dst(conn), addr, sizeof(addr)); // 获取连接设备的蓝牙地址

  printk("Pairing failed conn: %s, reason %d %s\n", addr, reason,
       bt_security_err_to_str(reason)); // 打印配对失败信息和失败原因
}

// 配对认证信息回调结构体
static struct bt_conn_auth_info_cb conn_auth_info_callbacks = {
  .pairing_complete = pairing_complete, // 配对完成回调
  .pairing_failed = pairing_failed      // 配对失败回调
};


static void bt_ready(int err)
{
    if (err) {
      LOG_ERR("Bluetooth init failed (err %d)\n", err);
      return;
    }
    if (IS_ENABLED(CONFIG_SETTINGS))
    {
      int ret = settings_load_subtree("bt");  // 加载蓝牙设置子树settings_load();
      if (ret)
      {
        LOG_ERR("settings_load failed (err %d)\n", ret);
      }
    }
    BLE_PPL_Gatt_Services_Init();
}

int central_peripheral_init(void)
{
  int err;
  BLE_ATT_COM_FUNC_Init();//初始化蓝牙ATT通信数据处理函数
  //--------------------------------设置MAC地址--------------------------------------------
  // bt_addr_le_t addr;
  // // create device address
  // bt_addr_le_from_str("FF:EE:DD:CC:BB:AA", "public", &addr);
  // bt_id_create(&addr, NULL);

  bt_passkey_set(CONFIG_DGLAB_BLUETOOTH_PAIRING_CODE);
  bt_conn_cb_register(&conn_callbacks);  // 注册连接回调函数

  err = bt_conn_auth_cb_register(&conn_auth_callbacks);
	if (err) {
		printk("Failed to register authorization callbacks.\n");
		return 0;
	}
  err = bt_conn_auth_info_cb_register(&conn_auth_info_callbacks);
	if (err) {
		printk("Failed to register authorization info callbacks.\n");
		return 0;
	}

  err = bt_enable(bt_ready);
  if (err)
  {
    LOG_ERR("Bluetooth init failed (err %d)\n", err);
    return err;
  }
  
  LOG_DBG("Bluetooth initialized\n");
  //--------------------------------中央模式下扫描外围设备--------------------------------------------
  // BLE_CTL_LE_start_scan_begin();
  // k_work_schedule(&timestamp_send_work, K_SECONDS(5));//发送数据
  LOG_DBG("peripheral_start\n");
  k_work_init(&BLE_INFO.PPL_CONN.adv_work, adv_work_handler);//开一个工作队列 通过队列形式进行广播任务
  advertising_start();  // 开始广播
  return 1;
}

// static void on_timestamp_send_timeout(struct k_work *work);
// K_WORK_DELAYABLE_DEFINE(timestamp_send_work, on_timestamp_send_timeout);
// 测试发送gatt数据
// static void send_timestamp_to_peripheral(struct bt_conn *conn, void *data)
// {
//     // LOG_INF("---peripheral---\r\n");
//     int err;
//     struct bt_conn_info conn_info;
//     static uint8_t num[4] = {0};
//     // uint64_t toggle_time_us = *(uint64_t *)data;

//     // 获取连接信息
//     err = bt_conn_get_info(conn, &conn_info);
//     if (err)
//     {
//         LOG_ERR("---bt_conn_get_info---\r\n");
//         return; // 如果获取失败，直接返回
//     }

//     // 获取连接索引
//     uint8_t conn_index = bt_conn_index(conn);

//     // 检查连接状态是否为已连接
//     if (conn_info.state != BT_CONN_STATE_CONNECTED)
//     {

//         LOG_ERR("---BT_CONN_STATE_CONNECTED---\r\n");
//         return; // 如果不是已连接状态，直接返回
//     }

//     // // 检查服务发现是否完成
//     if (BLE_INFO.CTL_CONN[conn_index].subscribe_params.value_handle == 0)
//     {
//         /* 服务发现尚未完成 */
//         return;
//     }

//     // 向外设写入时间动作消息num
//     num[conn_index]++;
//     char senddata[10];

//     snprintf(senddata, sizeof(senddata), "num:%d\n", num[conn_index]);
//     err = bt_gatt_write_without_response(conn,
//                                          BLE_INFO.CTL_CONN[conn_index].gatt_write_handle,
//                                          senddata,
//                                          strlen(senddata),
//                                          false);
//     if (err)
//     {
//         printk("Failed writing to characteristic\n"); // 写入失败时打印错误信息
//     }
//     // else {
//     // 	// printk("Sent to conn index %d: ", conn_index);  // 写入成功时打印发送信息
//     // }
// }

// static void on_timestamp_send_timeout(struct k_work *work)
// {
//     // uint64_t local_time_us = controller_time_us_get();
//     // LOG_INF("---on_timestamp_send_timeout---\r\n");
//     bt_conn_foreach(BT_CONN_TYPE_LE, send_timestamp_to_peripheral, NULL); // 遍历

//     k_work_schedule(k_work_delayable_from_work(work), K_SECONDS(1)); // 1HZ
// }
