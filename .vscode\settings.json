{
    "files.associations": {
        "ratio": "c",
        "system_error": "c",
        "array": "c",
        "functional": "c",
        "tuple": "c",
        "type_traits": "c",
        "utility": "c",
        "conn.h": "c",
        "printk.h": "c",
        "typeinfo": "c",
        "compare": "c",
        "hci.h": "c",
        "log.h": "c",
        "byteorder.h": "c",
        "string_view": "c",
        "tracing_sysview_syscall.h": "c",
        "types.h": "c",
        "stddef.h": "c",
        "uuid.h": "c",
        "gatt.h": "c",
        "peripheral_gatt.h": "c",
        "bluetooth.h": "c",
        "errno.h": "c",
        "central_and_peripheral.h": "c",
        "random": "c",
        "kernel.h": "c",
        "slist.h": "c",
        "ble_blacklist.h": "c",
        "sys_heap.h": "c",
        "shell.h": "c",
        "string": "c",
        "flash.h": "c",
        "netfwd": "c",
        "reboot.h": "c",
        "flash_map.h": "c",
        "list": "c",
        "ble_maclist.h": "c",
        "system_config.h": "c",
        "string.h": "c",
        "flash_control.h": "c",
        "memory": "c",
        "display.h": "c",
        "settings.h": "c",
        "bit": "c",
        "cstddef": "c",
        "limits": "c",
        "new": "c",
        "ble_att_com.h": "c",
        "zms.h": "c",
        "device.h": "c",
        "kernel_includes.h": "c",
        "stdint.h": "c",
        "util.h": "c",
        "ble_staticmaclist.h": "c",
        "atomic": "c",
        "cstdint": "c",
        "game_common.h": "c",
        "cmath": "c",
        "numeric": "c",
        "cinttypes": "c",
        "cstdlib": "c",
        "addr.h": "c",
        "hci_types.h": "c",
        "iterable_sections.h": "c",
        "hci_core.h": "c",
        "watchdog.h": "c",
        "uart_com.h": "c"
    },
    "C_Cpp.clang_format_style": "{ BasedOnStyle: Google ,ColumnLimit: 200,BreakBeforeBraces: Allman,AllowShortFunctionsOnASingleLine: false,AlignConsecutiveMacros: AcrossEmptyLinesAndComments,AlignConsecutiveAssignments: true}",//谷歌风格、每行不超过200、大括号单独一行、不允许在一行中编写简短的函数、禁用注释对齐、对齐宏定义
    "editor.formatOnSave": false,//保存自动格式化
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
}