# 启用Shell和内存命令
# CONFIG_SHELL=y
# CONFIG_KERNEL_SHELL=y
# CONFIG_THREAD_MONITOR=y
# CONFIG_INIT_STACKS=y
# CONFIG_THREAD_STACK_INFO=y

CONFIG_LOG=y
#后端的颜色
CONFIG_LOG_BACKEND_SHOW_COLOR=y
CONFIG_UART_CONSOLE=y
#将 HCI 错误代码以字符串形式打印
CONFIG_BT_HCI_ERR_TO_STR=y

#RTT
CONFIG_USE_SEGGER_RTT=n
CONFIG_LOG_BACKEND_RTT=n
CONFIG_RTT_CONSOLE=n


CONFIG_MAIN_STACK_SIZE=4096

CONFIG_BT=y #蓝牙
CONFIG_BT_SCAN=y    # 启用蓝牙扫描功能
CONFIG_BT_PERIPHERAL=y #外围设备角色支持
# CONFIG_BT_DEVICE_NAME="47L122000"
# CONFIG_BT_DEVICE_APPEARANCE=832   # 设置蓝牙设备外观特征值
CONFIG_BT_CENTRAL=y #中心角色支持
#关闭自动PHY更新 测试中：->华为鸿蒙会断连 报：0x2a BT_HCI_ERR_DIFF_TRANS_COLLISION
CONFIG_BT_AUTO_PHY_UPDATE=n 
CONFIG_BT_PRIVACY=n #设备隐私
CONFIG_BT_GATT_DM=y #启用GATT发现管理器
CONFIG_BT_EXT_ADV=y #多广播
CONFIG_BT_CTLR_ADV_EXT=y    #启用控制器扩展广播

# #L2CAP TX 缓冲区支持的最大 L2CAP MTU
# CONFIG_BT_L2CAP_TX_MTU=66 
# #支持的最大数据长度
# CONFIG_BT_CTLR_DATA_LENGTH_MAX=38 
# #传入数据支持的最大 ACL 大小
# CONFIG_BT_BUF_ACL_RX_SIZE=37 
# #传出数据支持的最大 ACL 大小
# CONFIG_BT_BUF_ACL_TX_SIZE=37 
# #支持的最大 HCI 事件缓冲区长度
# CONFIG_BT_BUF_EVT_RX_SIZE=255 
# #最大支持 HCI 命令缓冲区长度
# CONFIG_BT_BUF_CMD_TX_SIZE=255 

#接收线程堆栈的大小
# CONFIG_BT_RX_STACK_SIZE=3092
#系统工作队列堆栈大小
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=3072
#堆内存池大小（以字节为单位）
CONFIG_HEAP_MEM_POOL_SIZE=3072

#连接长度改变
CONFIG_BT_USER_PHY_UPDATE=n
# CONFIG_BT_CTLR_PHY_2M=y
#最大同时连接数
CONFIG_BT_MAX_CONN=7    
# 设置最大配对设备数
CONFIG_BT_MAX_PAIRED=5  
#传入 ACL 数据缓冲区的数量 number of incoming ACL data buffers is equal to CONFIG_BT_MAX_CONN + 1
CONFIG_BT_BUF_ACL_RX_COUNT_EXTRA=8  
 #传出 ACL 数据缓冲区的数量 4 if CONFIG_BT_MESH_GATT
CONFIG_BT_BUF_ACL_TX_COUNT= 7  
#此选项启用对 GATT 客户端角色的支持。
CONFIG_BT_GATT_CLIENT=y 
 # 如关闭多重读取功能
CONFIG_BT_GATT_READ_MULTIPLE=n     
# 增加ATT准备写入的缓冲区数量
CONFIG_BT_ATT_PREPARE_COUNT=7       
 
# CONFIG_BT_L2CAP_TX_BUF_COUNT=15

#此选项启用对 Security Manager 协议的支持 （SMP） 实现 LTE，从而可以通过 LE 配对设备。
# 启用安全管理
CONFIG_BT_SMP=y
# CONFIG_TFM_IPC=n
# CONFIG_TFM_PROFILE_TYPE_SMALL=y
# CONFIG_TFM_PROFILE_TYPE_MEDIUM=y
# # 1=最小隔离，2=中等隔离，3=最高隔离
# CONFIG_TFM_ISOLATION_LEVEL=2   
# CONFIG_BT_SIGNING=y
# CONFIG_BT_SMP_SC_ONLY=y



CONFIG_ZMS=y
# CONFIG_PM_PARTITION_SIZE_ZMS_STORAGE=0x1900
# CONFIG_PM_PARTITION_SIZE_SETTINGS_STORAGE=0x4000

###########################################外围设备角色###############################################
#并发数
# CONFIG_BT_CTLR_SDC_PERIPHERAL_COUNT=1

#用户控制数据长度更新程序
# CONFIG_BT_USER_DATA_LEN_UPDATE=y
# #浮点
# CONFIG_FPU=y

#可绑定模式
CONFIG_BT_BONDABLE=y
#持久存储蓝牙状态和配置
CONFIG_BT_SETTINGS=y
CONFIG_FLASH=y
CONFIG_FLASH_PAGE_LAYOUT=y
CONFIG_FLASH_MAP=y


CONFIG_BT_LL_SOFTDEVICE=y
#启用子系统 用于存储
CONFIG_SETTINGS=y
# CONFIG_NVS=y
CONFIG_SETTINGS_FILE=y
CONFIG_SETTINGS_FILE_PATH="/lfs/settings"

# CONFIG_BT_KEYS_OVERWRITE_OLDEST=y

#使用固定配对码
CONFIG_BT_FIXED_PASSKEY=y
CONFIG_DGLAB_BLUETOOTH_PAIRING_CODE=000000

#随机数
# CONFIG_NRF_SECURITY=y
# CONFIG_MBEDTLS_PSA_CRYPTO_C=y
# CONFIG_PSA_WANT_GENERATE_RANDOM=y

CONFIG_DGLAB_LVGL_DISPLAY_ST7789=y
###################################################LVGL######################################################
CONFIG_LV_Z_MEM_POOL_SIZE=16384
CONFIG_LV_Z_SHELL=y

CONFIG_DISPLAY=y
CONFIG_DISPLAY_LOG_LEVEL_ERR=y

CONFIG_LVGL=y

# CONFIG_LV_USE_SYSMON=y #启用系统监视器组件
CONFIG_DISPLAY=y#启用系统监视器组件
CONFIG_LV_USE_PERF_MONITOR=y #显示 CPU 使用率和 FPS 计数
CONFIG_LV_USE_MEM_MONITOR=y#显示已使用的内存和内存碎片

CONFIG_LV_USE_LOG=n
CONFIG_LV_USE_LABEL=n
CONFIG_LV_USE_ARC=y
CONFIG_LV_USE_MONKEY=n
CONFIG_LV_FONT_MONTSERRAT_14=n
CONFIG_LV_COLOR_DEPTH_16=y

# CONFIG_LV_USE_IMGFONT=y #支持在标签或范围小部件中使用图像作为字体

# CONFIG_LV_FONT_MONTSERRAT_12=y
# CONFIG_LV_FONT_MONTSERRAT_14=y
# CONFIG_LV_FONT_MONTSERRAT_16=y
# CONFIG_LV_FONT_MONTSERRAT_18=y
CONFIG_LV_FONT_MONTSERRAT_24=y

# Benchmark Demo
# CONFIG_LV_USE_FONT_COMPRESSED=y

CONFIG_GPIO=y
CONFIG_SPI=y
CONFIG_MIPI_DBI=y
CONFIG_MIPI_DBI_SPI=y
CONFIG_ST7789V=y

#动态注册GATT
CONFIG_BT_GATT_DYNAMIC_DB=y


# 禁用TrustZone安全功能 
# CONFIG_ARM_TRUSTZONE_M=n
#串口
CONFIG_SERIAL=y
CONFIG_UART_ASYNC_API=y

#看门狗
CONFIG_WDT_LOG_LEVEL_DBG=y
CONFIG_WATCHDOG=y
CONFIG_WDT_DISABLE_AT_BOOT=n

#OLED PWM
CONFIG_PWM=y
CONFIG_PWM_LOG_LEVEL_DBG=y
# CONFIG_LED=y
# CONFIG_LED_PWM=y
