/**
 * @file BLE_MACList.h
 * @brief 蓝牙链表列表
 *
 * 该文件实现了蓝牙链表列表的管理功能
 *
 * @date 2025-06-10
 * <AUTHOR>
 */

#ifndef BLE_MACList_H
#define BLE_MACList_H
#include <zephyr/bluetooth/addr.h>
#include <zephyr/sys/slist.h>
// #define BLE_MACList_MAX_SIZE 10 // 定义单链表最大容量 防止无限申请内存空间

typedef struct _ble_list {
  sys_slist_t list; // 单链表头
  uint8_t Conn_Max; // 最大连接数 定义单链表最大容量 防止无限申请内存空间
  uint8_t count;    // 当前数量
} ST_Ble_MACList;



/**
 * @brief 初始化链表列表
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param set_conn_max 设置最大连接数
 */
void BLE_MACList_Init(ST_Ble_MACList *st_list,uint8_t set_conn_max);

/**
 * @brief 添加地址到列表
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 要添加的蓝牙地址
 * @return 0 成功, -ENOMEM 无空间, -EALREADY 已存在
 */
int BLE_MACList_Add(ST_Ble_MACList *st_list, const bt_addr_le_t *addr);

/**
 * @brief 从列表移除地址
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 要移除的蓝牙地址
 * @return 0: 成功, -ENOENT 未找到
 */
int BLE_MACList_Remove(ST_Ble_MACList *st_list, const bt_addr_le_t *addr);

/**
 * @brief 清空列表
 * @param st_list 链表列表 参考 ST_Ble_MACList
 */
void BLE_MACList_Clear(ST_Ble_MACList *st_list);

/**
 * @brief 检查地址是否在列表
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 要检查的蓝牙地址
 * @return true 在列表, false 不在
 */
bool BLE_MACList_Check(ST_Ble_MACList *st_list, const bt_addr_le_t *addr);

/**
 * @brief 查找指定MAC地址在列表中的位置
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 要查找的蓝牙地址
 * @return >=0: 位置索引（从0开始），-ENOENT 未找到
 */
int BLE_MACList_Check_Location(ST_Ble_MACList *st_list, const bt_addr_le_t *addr);

/**
 * @brief 获取列表数量
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @return u8类型返回已有数量
 */
uint8_t BLE_MACList_Get_Count(ST_Ble_MACList *st_list);

/**
 * @brief 判断链表列表是否为空
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @return true 为空, false 非空
 */
bool BLE_MACList_Is_Empty(ST_Ble_MACList *st_list);

/**
 * @brief 获取列表中的第一个MAC地址
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 输出第一个MAC地址
 * @return 0: 成功, -ENOENT 列表为空
 */
int BLE_MACList_Get_First(ST_Ble_MACList *st_list, bt_addr_le_t *addr);

/**
 * @brief 获取列表中指定位置的MAC地址
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param index 目标位置（从0开始）
 * @param addr 输出MAC地址
 * @return 0: 成功, -ENOENT 越界
 */
int BLE_MACList_Get_At(ST_Ble_MACList *st_list, uint8_t index, bt_addr_le_t *addr);

/**
 * @brief 通过MAC地址查找CTL_Index
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 要查找的蓝牙地址
 * @param ctl_index 查找到的CTL_Index输出指针
 * @return 0: 成功, -ENOENT 未找到
 */
int BLE_MACList_Get_CTL_Index(ST_Ble_MACList *st_list, const bt_addr_le_t *addr, uint8_t *ctl_index);

/**
 * @brief 添加地址和CTL_Index到列表
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 要添加的蓝牙地址
 * @param ctl_index 要设置的CTL_Index
 * @return 0 成功, -ENOMEM 无空间, -EALREADY 已存在
 */
int BLE_MACList_Add_With_CTL_Index(ST_Ble_MACList *st_list, const bt_addr_le_t *addr, uint8_t ctl_index);

/**
 * @brief 通过MAC地址修改CTL_Index值
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 要查找的蓝牙地址
 * @param ctl_index 新的CTL_Index值
 * @return 0: 成功, -ENOENT 未找到
 */
int BLE_MACList_Set_CTL_Index(ST_Ble_MACList *st_list, const bt_addr_le_t *addr, uint8_t ctl_index);

void BLE_MACList_Test(void);
#endif // BLE_MACList_H