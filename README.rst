.. zephyr:code-sample:: ble_central_multilink
   :name: Central Multilink
   :relevant-api: bluetooth

   Scan, connect and establish connection to up to 62 peripherals.

Overview
********

Application demonstrating Bluetooth LE Central role functionality by scanning for other
BLE devices and establishing connection to up to 62 peripherals with a strong
enough signal.

Requirements
************

* BlueZ running on the host, or
* A board with Bluetooth LE support

Building and Running
********************
This sample can be found under :zephyr_file:`samples/bluetooth/central_multilink`
in the Zephyr tree.

See :zephyr:code-sample-category:`bluetooth` samples for details.
