/**
 * @file BLE_MACList.c
 * @brief 蓝牙链表列表
 *
 * 该文件实现了蓝牙链表列表的管理功能
 *
 * @date 2025-06-10
 * <AUTHOR>
 */
#include "BLE_MACList.h"
#include "central_and_peripheral.h"
#include <zephyr/sys/slist.h>
#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(List,  LOG_LEVEL_ERR);

struct ble_list_entry
{
  bt_addr_le_t addr;
  uint8_t CTL_Index; 
  sys_snode_t node;
};



/**
 * @brief 初始化链表列表
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param set_conn_max 设置最大连接数
 */
void BLE_MACList_Init(ST_Ble_MACList *st_list,uint8_t set_conn_max)
{
  sys_slist_init(&st_list->list);
  st_list->count = 0;
  st_list->Conn_Max = set_conn_max;
  LOG_DBG("BLE MACList Init\n");
}

/**
 * @brief 添加地址到列表
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 要添加的蓝牙地址
 * @return 0 成功, -ENOMEM 无空间, -EALREADY 已存在
 */
int BLE_MACList_Add(ST_Ble_MACList *st_list,const bt_addr_le_t *addr)
{
  struct ble_list_entry *entry;
  sys_snode_t *node;
  // 检查地址是否已存在
  SYS_SLIST_FOR_EACH_NODE(&st_list->list, node)
  {
    entry = CONTAINER_OF(node, struct ble_list_entry, node);
    if (bt_addr_le_cmp(&entry->addr, addr) == 0)
    {
      return -EALREADY;
    }
  }

  if (st_list->count >= st_list->Conn_Max )
  {
    return -ENOMEM;
  }

  entry = (struct ble_list_entry *)k_malloc(sizeof(struct ble_list_entry));
  if (!entry)
  {
    return -ENOMEM;
  }

  bt_addr_le_copy(&entry->addr, addr);
  sys_slist_append(&st_list->list, &entry->node);
  st_list->count++;
  LOG_DBG("Added to BLE MACList: \n");
  return 0;
}

/**
 * @brief 从列表移除地址
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 要移除的蓝牙地址
 * @return 0: 成功, -ENOENT 未找到
 */
int BLE_MACList_Remove(ST_Ble_MACList *st_list,const bt_addr_le_t *addr)
{
  sys_snode_t *prev = NULL, *node;
  struct ble_list_entry *entry;

  SYS_SLIST_FOR_EACH_NODE(&st_list->list, node)
  {
    entry = CONTAINER_OF(node, struct ble_list_entry, node);
    if (bt_addr_le_cmp(&entry->addr, addr) == 0)
    {
      sys_slist_remove(&st_list->list, prev, node);
      k_free(entry);
      st_list->count--;
      LOG_DBG("Removed from BLE MACList\n");
      return 0;
    }
    prev = node;
  }
  return -ENOENT;
}

/**
 * @brief 清空列表
 * @param st_list 链表列表 参考 ST_Ble_MACList
 */
void BLE_MACList_Clear(ST_Ble_MACList *st_list)
{
  sys_snode_t *node, *next;
  struct ble_list_entry *entry;

  SYS_SLIST_FOR_EACH_NODE_SAFE(&st_list->list, node, next)
  {
    entry = CONTAINER_OF(node, struct ble_list_entry, node);
    sys_slist_remove(&st_list->list, NULL, node);
    k_free(entry);
  }
  st_list->count = 0;
  LOG_DBG("BLE MACList Cleared\n");
}

/**
 * @brief 检查地址是否在列表
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 要检查的蓝牙地址
 * @return true 在列表, false 不在
 */
bool BLE_MACList_Check(ST_Ble_MACList *st_list,const bt_addr_le_t *addr)
{
  sys_snode_t *node;
  struct ble_list_entry *entry;

  SYS_SLIST_FOR_EACH_NODE(&st_list->list, node)
  {
    entry = CONTAINER_OF(node, struct ble_list_entry, node);
    if (bt_addr_le_cmp(&entry->addr, addr) == 0)
    {
      LOG_DBG("Address is in BLE MACList\n");
      return true;
    }
  }
  return false;
}

/**
 * @brief 查找指定MAC地址在列表中的位置
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 要查找的蓝牙地址
 * @return >=0: 位置索引（从0开始），-ENOENT 未找到
 */
int BLE_MACList_Check_Location(ST_Ble_MACList *st_list, const bt_addr_le_t *addr)
{
  sys_snode_t *node;
  struct ble_list_entry *entry;
  int index = 0;

  SYS_SLIST_FOR_EACH_NODE(&st_list->list, node)
  {
    entry = CONTAINER_OF(node, struct ble_list_entry, node);
    if (bt_addr_le_cmp(&entry->addr, addr) == 0)
    {
      return index;
    }
    index++;
  }
  return -ENOENT;
}

/**
 * @brief 获取列表数量
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @return u8类型返回已有数量
 */
uint8_t BLE_MACList_Get_Count(ST_Ble_MACList *st_list)
{
  return st_list->count;
}

/**
 * @brief 判断链表列表是否为空
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @return true 为空, false 非空
 */
bool BLE_MACList_Is_Empty(ST_Ble_MACList *st_list)
{
  return (st_list->count == 0);
}
/**
 * @brief 获取列表中的第一个MAC地址
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 输出第一个MAC地址
 * @return 0: 成功, -ENOENT 列表为空
 */
int BLE_MACList_Get_First(ST_Ble_MACList *st_list, bt_addr_le_t *addr)
{
  sys_snode_t *node = sys_slist_peek_head(&st_list->list);
  if (!node)
  {
    return -ENOENT;
  }
  struct ble_list_entry *entry = CONTAINER_OF(node, struct ble_list_entry, node);
  if (addr)
  {
    bt_addr_le_copy(addr, &entry->addr);
  }
  return 0;
}
/**
 * @brief 获取列表中指定位置的MAC地址
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param index 目标位置（从0开始）
 * @param addr 输出MAC地址
 * @return 0: 成功, -ENOENT 越界
 */
int BLE_MACList_Get_At(ST_Ble_MACList *st_list, uint8_t index, bt_addr_le_t *addr)
{
  if (index >= st_list->count)
  {
    return -ENOENT;
  }
  sys_snode_t *node;
  struct ble_list_entry *entry;
  uint8_t i = 0;
  SYS_SLIST_FOR_EACH_NODE(&st_list->list, node)
  {
    if (i == index)
    {
      entry = CONTAINER_OF(node, struct ble_list_entry, node);
      if (addr)
      {
        bt_addr_le_copy(addr, &entry->addr);
      }
      return 0;
    }
    i++;
  }
  return -ENOENT;
}
/**
 * @brief 通过MAC地址查找CTL_Index
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 要查找的蓝牙地址
 * @param ctl_index 查找到的CTL_Index输出指针
 * @return 0: 成功, -ENOENT 未找到
 */
int BLE_MACList_Get_CTL_Index(ST_Ble_MACList *st_list, const bt_addr_le_t *addr, uint8_t *ctl_index)
{
  sys_snode_t *node;
  struct ble_list_entry *entry;

  SYS_SLIST_FOR_EACH_NODE(&st_list->list, node)
  {
    entry = CONTAINER_OF(node, struct ble_list_entry, node);
    if (bt_addr_le_cmp(&entry->addr, addr) == 0)
    {
      if (ctl_index) 
      {
        *ctl_index = entry->CTL_Index;
      }
      return 0;
    }
  }
  return -ENOENT;
}


/**
 * @brief 添加地址和CTL_Index到列表
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 要添加的蓝牙地址
 * @param ctl_index 要设置的CTL_Index
 * @return 0 成功, -ENOMEM 无空间, -EALREADY 已存在
 */
int BLE_MACList_Add_With_CTL_Index(ST_Ble_MACList *st_list, const bt_addr_le_t *addr, uint8_t ctl_index)
{
  struct ble_list_entry *entry;
  sys_snode_t *node;
  // 检查地址是否已存在
  SYS_SLIST_FOR_EACH_NODE(&st_list->list, node)
  {
    entry = CONTAINER_OF(node, struct ble_list_entry, node);
    if (bt_addr_le_cmp(&entry->addr, addr) == 0)
    {
      return -EALREADY;
    }
  }

  if (st_list->count >= st_list->Conn_Max )
  {
    return -ENOMEM;
  }

  entry = (struct ble_list_entry *)k_malloc(sizeof(struct ble_list_entry));
  if (!entry)
  {
    return -ENOMEM;
  }

  bt_addr_le_copy(&entry->addr, addr);
  entry->CTL_Index = ctl_index;
  sys_slist_append(&st_list->list, &entry->node);
  st_list->count++;
  LOG_DBG("Added to BLE MACList with CTL_Index: %d\n", ctl_index);
  return 0;
}

/**
 * @brief 通过MAC地址修改CTL_Index值
 * @param st_list 链表列表 参考 ST_Ble_MACList
 * @param addr 要查找的蓝牙地址
 * @param ctl_index 新的CTL_Index值
 * @return 0: 成功, -ENOENT 未找到
 */
int BLE_MACList_Set_CTL_Index(ST_Ble_MACList *st_list, const bt_addr_le_t *addr, uint8_t ctl_index)
{
  sys_snode_t *node;
  struct ble_list_entry *entry;

  SYS_SLIST_FOR_EACH_NODE(&st_list->list, node)
  {
    entry = CONTAINER_OF(node, struct ble_list_entry, node);
    if (bt_addr_le_cmp(&entry->addr, addr) == 0)
    {
      entry->CTL_Index = ctl_index;
      return 0;
    }
  }
  return -ENOENT;
}
//---------------------------------测试-----------------------------------
static ST_Ble_MACList BLE_List_MACList = {0};
void BLE_MACList_Test(void)
{
  bt_addr_le_t addr1, addr2;
  // 创建两个测试地址
  bt_addr_le_from_str("11:22:33:44:55:66", "random", &addr1);
  bt_addr_le_from_str("AA:BB:CC:DD:EE:FF", "random", &addr2);

  // 初始化列表
  BLE_MACList_Init(&BLE_List_MACList,10);

  // 添加两个地址并打印数量
  BLE_MACList_Add(&BLE_List_MACList, &addr1);
  BLE_MACList_Add(&BLE_List_MACList, &addr2);
  LOG_DBG("Current count after adding: %d\n", BLE_MACList_Get_Count(&BLE_List_MACList));

  // 检查两个地址是否存在
  LOG_DBG("Check addr1 exists: %d\n", BLE_MACList_Check(&BLE_List_MACList, &addr1));
  LOG_DBG("Check addr2 exists: %d\n", BLE_MACList_Check(&BLE_List_MACList, &addr2));

  // 删除第二个地址
  BLE_MACList_Remove(&BLE_List_MACList, &addr2);

  // 再次检查两个地址并打印数量
  LOG_DBG("Check addr1 exists: %d\n", BLE_MACList_Check(&BLE_List_MACList, &addr1));
  LOG_DBG("Check addr2 exists: %d\n", BLE_MACList_Check(&BLE_List_MACList, &addr2));
  LOG_DBG("Current count after removing addr2: %d\n", BLE_MACList_Get_Count(&BLE_List_MACList));

  // 清空列表
  BLE_MACList_Clear(&BLE_List_MACList);

  // 最后检查两个地址并打印数量
  LOG_DBG("Check addr1 exists: %d\n", BLE_MACList_Check(&BLE_List_MACList, &addr1));
  LOG_DBG("Check addr2 exists: %d\n", BLE_MACList_Check(&BLE_List_MACList, &addr2));
  LOG_DBG("Current count after clearing: %d\n", BLE_MACList_Get_Count(&BLE_List_MACList));
}
