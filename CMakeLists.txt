# SPDX-License-Identifier: Apache-2.0

cmake_minimum_required(VERSION 3.20.0)
find_package(Zephyr REQUIRED HINTS $ENV{ZEPHYR_BASE})
project(NRF54_BLE)


set(LVGL_DIR ${ZEPHYR_LVGL_MODULE_DIR})
FILE(GLOB app_sources src/*.c  src/LVGL/*.c src/LIB/*.c src/LVGL/ui/*.c src/BLE/*.c src/GAME/*.c src/CODE/*.c)

target_sources(app PRIVATE
  ${app_sources}
  # src/main.c
  # src/central_multilink.c
)

target_include_directories(app PRIVATE
    ${LVGL_DIR}/demos/ 
    src/
    src/LIB/
    src/BLE/
    src/BLE/include/
    src/GAME/
    src/CODE/
)

zephyr_library_include_directories(${ZEPHYR_BASE}/samples/bluetooth)
