/**
 * @file BLE_CTL.c
 * @brief 蓝牙中央设备模式
 *
 * 该文件实现了蓝牙中央设备模式下连接多台蓝牙设备 设备数量通过prj.conf下CONFIG_BT_MAX_CONN设置
 * 包含：扫描广播数据、连接BLE、订阅notify。
 *
 * @date 2025-05-27
 * <AUTHOR>
 */
#include <zephyr/logging/log.h>
#include "central_and_peripheral.h"
LOG_MODULE_REGISTER(CTL, LOG_LEVEL_DBG);


struct bt_conn *CTL_conn_connecting; /* 正在连接的蓝牙连接 */
struct bt_conn *CTL_conn_connecting_MAC; /* 正在连接的蓝牙连接 */
#define ADV_NAME_STR_MAX_LEN (20)  // 广播包名称最大长度

struct bt_conn_le_create_param create_param = {
    .options = BT_CONN_LE_OPT_NONE,
    .interval = INIT_INTERVAL,
    .window = INIT_WINDOW,
    .interval_coded = 0,
    .window_coded = 0,
    .timeout = 0,
};
struct bt_le_conn_param conn_param = {
    .interval_min = CONN_INTERVAL,
    .interval_max = CONN_INTERVAL,
    .latency = CONN_LATENCY,
    .timeout = CONN_TIMEOUT,
};
 
// // 根据MAC地址匹配连接
// const char addr_str_MAC[4][17] = {
//     {"50:54:7B:E8:44:DE"},
//     {"50:54:7B:E8:44:CD"},
//     {"50:54:7B:E8:45:13"},
//     {"50:54:7B:E8:44:B3"},
// };
// const char *paddr_str_MAC = addr_str_MAC[0];

// // 根据广播名字匹配
// const char addr_str_name[][15] = {
//     {"NRF54_WCH1"},
//     {"NRF54_WCH2"},
//     {"NRF54_WCH3"},
//     {"NRF54_WCH4"},
// };
// const char *paddr_str_name = addr_str_name[0];
// const char Select_BLE_Name[]="NRF54_WCH";//选择蓝牙名字
const char Select_BLE_Name[]="47L120";//选择蓝牙名字
typedef struct
{
  char name_str[15];
  uint8_t name_len;
  char addr_str[BT_ADDR_LE_STR_LEN];
} broadcast;  // 广播数据

/// @brief 中央设备连接回调
void BLE_CTL_GATT_FUNC_CB_Init(BLE_CTL_notify_callback_t notify_cb)
{
  BLE_INFO.CTL_FUNC.Notify_CB  = notify_cb;
}

/**************************************************************
 * @brief 广播包解析回调
 *
 * @param *data 广播数据
 * @param *user_data 用户数据指针
 * @param user_data 用户数据指针
 *
 * @return  NONE
 ***************************************************************/
static bool BLE_CTL_broadcast_data_parse_CB(struct bt_data *data, void *user_data)
{
  broadcast *info = user_data;
  uint8_t len;
  // printk("data->type: %d  data->data_len: %d  data->data:%x	\n", data->type,data->data_len,data->data);
  switch (data->type)
  {
    case BT_DATA_NAME_SHORTENED:
    case BT_DATA_NAME_COMPLETE:
      len = MIN(data->data_len, ADV_NAME_STR_MAX_LEN - 1);
      memcpy(info->name_str, data->data, len);
      info->name_str[len] = '\0';
      info->name_len = len;
      // printk("name: %s len: \n", info->name_str, info->name_len);
      return false;
    default:
      return true;
  }
}

/**************************************************************
 * @brief 通过MAC地址直接连接设备
 *
 * @param addr_str 设备MAC地址字符串
 *
 * @return 0 成功，负值为错误码
 ***************************************************************/
int BLE_CTL_connect_by_mac(const bt_addr_le_t *addr)
{
  struct bt_conn_le_create_param create_param1 = {
      .options = BT_CONN_LE_OPT_NONE,
      .interval = INIT_INTERVAL,
      .window = INIT_WINDOW,
      .interval_coded = 0,
      .window_coded = 0,
      .timeout = 0,
  };
  struct bt_le_conn_param conn_param1 = {
      .interval_min = CONN_INTERVAL,
      .interval_max = CONN_INTERVAL,
      .latency = CONN_LATENCY,
      .timeout = 200,
  };
  if (CTL_conn_connecting_MAC)
  {
     LOG_DBG("Already connecting\n");
    return -EBUSY;
  }
  bt_le_scan_stop();
  return bt_conn_le_create(addr, &create_param1, &conn_param1, &CTL_conn_connecting_MAC);
}
/**************************************************************
 * @brief 发现设备时的回调函数
 *
 * @param *addr 设备地址
 * @param rssi 信号强度
 * @param type 广播类型
 * @param *ad 广播数据
 *
 * @return  NONE
 ***************************************************************/
static void BLE_CTL_device_found(const bt_addr_le_t *addr, int8_t rssi, uint8_t type, struct net_buf_simple *ad)
{
 
  // char name_str[ADV_NAME_STR_MAX_LEN] = {0};
  // char addr_str[BT_ADDR_LE_STR_LEN];
  broadcast broadcast_info = {.name_str = {0}};
  int err;

  if (BLE_INFO.CTL_CONF.CONN)
  {
    LOG_ERR("SET NULL");
    return;
  }

  /* We're only interested in connectable events */
  if (type != BT_GAP_ADV_TYPE_ADV_IND && type != BT_GAP_ADV_TYPE_ADV_DIRECT_IND && type != BT_GAP_ADV_TYPE_EXT_ADV)
  {
    return;
  }

  // /* connect only to devices in close proximity */
  if (rssi < BLE_Scan_RSSI_TH)
  {
    return;
  }
  // bt_addr_le_to_str(addr, addr_str, sizeof(addr_str));
  // printk("Device found: %s (RSSI %d)\n", addr_str, rssi);

  bt_data_parse(ad, BLE_CTL_broadcast_data_parse_CB, &broadcast_info);
  bt_addr_le_to_str(addr, broadcast_info.addr_str, sizeof(broadcast_info.addr_str));
  LOG_DBG("MAC:%s RSSI:%d Name:%s", broadcast_info.addr_str, rssi, broadcast_info.name_str);

  // for (uint8_t num = 0; num < 4; num++)
  // {
    // if (strncmp(broadcast_info.addr_str, (paddr_str_MAC + num * 17), 17) == 0) // 綁定MAC
    // if (strncmp(broadcast_info.name_str, (paddr_str_name + num * 15), broadcast_info.name_len) == 0)  // 綁定SSID
    if (strncmp(broadcast_info.name_str, Select_BLE_Name, sizeof(Select_BLE_Name)-1) == 0)//筛选名字前缀相同
    {
      if(BLE_MACList_Get_Count(&BLE_List_BlackList) > 0 && BLE_MACList_Check(&BLE_List_BlackList,addr) == true)//黑名单有设备&&在黑名单内的设备不连接
      {
        LOG_DBG("BlackList: %s (RSSI %d) %s\n", broadcast_info.addr_str, rssi, broadcast_info.name_str);
        return;
      }
      LOG_DBG("Connected: %s (RSSI %d) %s\n", broadcast_info.addr_str, rssi, broadcast_info.name_str);
      if (bt_le_scan_stop())
      {
        LOG_DBG("Scanning successfully stopped\n");
        return;
      }
      err = bt_conn_le_create(addr, &create_param, &conn_param, &BLE_INFO.CTL_CONF.CONN);
      if (err)
      {
        LOG_ERR("Create conn to %s failed (%d)\n", broadcast_info.addr_str, err);
        // BLE_CTL_LE_start_scan_begin();
      }
      // if (CTL_conn_connecting)
      // {
      //   bt_conn_unref(CTL_conn_connecting);  // 失败就断了
      // }
    }
  // }
}

/**************************************************************
 * @brief BLE开始扫描的函数
 *
 * @param  NONE
 *
 * @return NONE
 ***************************************************************/
void BLE_CTL_LE_start_scan_begin(void)
{
  struct bt_le_scan_param scan_param = {
      .type = BT_LE_SCAN_TYPE_PASSIVE,
      .options = BT_LE_SCAN_OPT_NONE,
      .interval = SCAN_INTERVAL,
      .window = SCAN_WINDOW,
  };
  int err;

  err = bt_le_scan_start(&scan_param, BLE_CTL_device_found);
  if (err)
  {
    LOG_ERR("Scanning failed to start (err %d)\n", err);
    return;
  }
  LOG_DBG("Scanning successfully started\n");
}

/**************************************************************
 * @brief 通知回调函数
 *
 * @param  *conn 连接句柄 参考：bt_conn
 * @param *params 订阅参数
 * @param *data 接收到的数据
 * @param length 数据长度
 *
 * @return  NONE
 ***************************************************************/
uint8_t BLE_CTL_notify_func_CB(struct bt_conn *conn, struct bt_gatt_subscribe_params *params, const void *data, uint16_t length)
{
  /* 如果没有数据，表示取消订阅 */
  if (!data)
  {
    LOG_DBG("[UNSUBSCRIBED]\n");
    params->value_handle = 0U;
    return BT_GATT_ITER_STOP;
  }
  if(BLE_INFO.CTL_FUNC.Notify_CB != NULL)
  {
    BLE_INFO.CTL_FUNC.Notify_CB(conn, data, length);
  }
  
  // {//测试 收到什么数据 发什么回去
  //   int err;
  //   err = bt_gatt_write_without_response(conn,
  //                                        BLE_INFO.CTL_CONN[index].gatt_write_handle,
  //                                        data,
  //                                        length,
  //                                        false);
  // }
  // LOG_DBG("Receive %d data:%s \n", index, data);
  return BT_GATT_ITER_CONTINUE;
}

/**************************************************************
 * @brief UUID服务发现回调函数
 *
 * @param  *conn 连接句柄 参考：bt_conn
 * @param *attr 发现的属性
 * @param *params 发现参数
 *
 * @return  NONE
 ***************************************************************/
static uint8_t BLE_CTL_UUID_discover_func_CB(struct bt_conn *conn, const struct bt_gatt_attr *attr, struct bt_gatt_discover_params *params)
{
  int err;
  if (!attr)
  {
    return BT_GATT_ITER_STOP;
  }
  struct bt_gatt_chrc *chrc = (struct bt_gatt_chrc *)attr->user_data;
  // uint8_t conn_index=BLE_INFO.CTL_CONF.Index_MAP[bt_conn_index(conn)];
  uint8_t conn_index=bt_conn_index(conn);
  switch (params->type)
  {
    case BT_GATT_DISCOVER_CHARACTERISTIC:  // 特征
    {
      // LOG_INF("CHARACTERISTIC\n");
      switch (((struct bt_uuid_16 *)chrc->uuid)->val)
      {
        case BLE_CTL_DGLAB_CHR_RX_UUID:
        {
          // LOG_DBG("0xff01 handle %u  UUID:0x%s \n", attr->handle, bt_uuid_str(attr->uuid));
          BLE_INFO.CTL_CONN[conn_index].subscribe_params.value_handle = bt_gatt_attr_value_handle(attr);
          BLE_INFO.CTL_CONN[conn_index].subscribe_params.notify = BLE_CTL_notify_func_CB;
          BLE_INFO.CTL_CONN[conn_index].subscribe_params.value = BT_GATT_CCC_NOTIFY;
          BLE_INFO.CTL_CONN[conn_index].subscribe_params.ccc_handle = attr->handle + 2;
          err = bt_gatt_subscribe(conn, &BLE_INFO.CTL_CONN[conn_index].subscribe_params);  // 订阅
          if (err && err != -EALREADY)
          {
            LOG_DBG("Subscribe failed (err %d)\n", err);
          }
        }
        break;
        case BLE_CTL_DGLAB_CHR_TX_UUID:
        {
          BLE_INFO.CTL_CONN[conn_index].gatt_write_handle = bt_gatt_attr_value_handle(attr);
        }
        break;
        default:
          break;
      }
    }
    break;
      // case  BT_GATT_DISCOVER_DESCRIPTOR://描述
      // {
      //     // LOG_INF("DESCRIPTOR\n");
      // }
      // break;
    default:
      break;
  }
  return BT_GATT_ITER_CONTINUE;
}

/**************************************************************
 * @brief 启动UUID服务查询
 *
 * @param  *conn 连接句柄 参考：bt_conn
 *
 * @return  NONE
 ***************************************************************/
void BLE_CLT_start_service_discovery(struct bt_conn *conn)
{
  int err;
  // uint8_t conn_index=BLE_INFO.CTL_CONF.Index_MAP[bt_conn_index(conn)];
  uint8_t conn_index=bt_conn_index(conn);
  BLE_INFO.CTL_CONN[conn_index].discover_params.uuid = NULL;
  BLE_INFO.CTL_CONN[conn_index].discover_params.func = BLE_CTL_UUID_discover_func_CB;
  BLE_INFO.CTL_CONN[conn_index].discover_params.start_handle = BT_ATT_FIRST_ATTRIBUTE_HANDLE;
  BLE_INFO.CTL_CONN[conn_index].discover_params.end_handle = BT_ATT_LAST_ATTRIBUTE_HANDLE;
  BLE_INFO.CTL_CONN[conn_index].discover_params.type = BT_GATT_DISCOVER_CHARACTERISTIC;//查特征值

  err = bt_gatt_discover(conn, &BLE_INFO.CTL_CONN[conn_index].discover_params);
  if (err)
  {
    LOG_DBG("Service discovery failed (err %d)\n", err);
    return;
  }
  LOG_DBG("Service discovery started\n");
}

// /**************************************************************
//  * @brief 快速回连功能：尝试快速连接已知设备
//  *
//  * @param  NONE
//  *
//  * @return NONE
//  ***************************************************************/
// void BLE_CTL_fast_reconnect(void)
// {
//   // 假设有一个保存已知设备MAC地址的列表 BLE_List_WhiteList
//   // 遍历白名单，尝试连接
//   struct bt_conn_le_create_param create_param = {
//     .options = BT_CONN_LE_OPT_NONE,
//     .interval = INIT_INTERVAL,
//     .window = INIT_WINDOW,
//     .interval_coded = 0,
//     .window_coded = 0,
//     .timeout = 0,
//   };
//   struct bt_le_conn_param conn_param = {
//     .interval_min = CONN_INTERVAL,
//     .interval_max = CONN_INTERVAL,
//     .latency = CONN_LATENCY,
//     .timeout = CONN_TIMEOUT,
//   };

//   for (uint8_t i = 0; i < BLE_MACList_Get_Count(&BLE_List_WhiteList); i++) 
//   {
//     const bt_addr_le_t *addr = BLE_MACList_Get_Addr(&BLE_List_WhiteList, i);
//     if (!addr) continue;
//     if (CTL_conn_connecting) break;
//     int err = bt_conn_le_create(addr, &create_param, &conn_param, &CTL_conn_connecting);
//     if (err) {
//       LOG_DBG("Fast reconnect to %d failed (%d)\n", i, err);
//       continue;
//     }
//     if (CTL_conn_connecting) {
//       LOG_DBG("Fast reconnect success\n");
//       break;
//     }
//   }
// }

/**************************************************************
 * @brief 发送数据到中央设备下数据
 *
 * @param  *conn 参考：bt_conn
 * @param *data 发送的数据
 * @param len 发送的数据长度
 *
 * @return  参考：errno.h
 ***************************************************************/
int BLE_CTL_Write(struct bt_conn *conn,const uint8_t *data, uint16_t len)
{
    int err;
    // uint8_t conn_index=BLE_INFO.CTL_CONF.Index_MAP[bt_conn_index(conn)];
    uint8_t conn_index=bt_conn_index(conn);
    err = bt_gatt_write_without_response(conn,
                                         BLE_INFO.CTL_CONN[conn_index].gatt_write_handle,
                                         data,
                                         len,
                                         false);
    return err;
}
