// To get started, press Ctrl+Space to bring up the completion menu and view the available nodes.

// You can also use the buttons in the sidebar to perform actions on nodes.
// Actions currently available include:

// * Enabling / disabling the node
// * Adding the bus to a bus
// * Removing the node
// * Connecting ADC channels

// For more help, browse the DeviceTree documentation at https://docs.zephyrproject.org/latest/guides/dts/index.html
// You can also visit the nRF DeviceTree extension documentation at https://docs.nordicsemi.com/bundle/nrf-connect-vscode/page/guides/ncs_configure_app.html#devicetree-support-in-the-extension

/ {
    chosen {
        zephyr,display = &st7789v;
		zephyr,sram = &cpuapp_sram;
    };
    mipi_st7789v {
		compatible = "zephyr,mipi-dbi-spi";
		spi-dev = <&spi00>;
		dc-gpios = <&gpio1 11 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio1 12 GPIO_ACTIVE_LOW>;
		write-only;
		#address-cells = <1>;
		#size-cells = <0>;
        st7789v: st7789v@0 {
            status = "okay";
            compatible = "sitronix,st7789v";
            mipi-max-frequency = <4000000>;
			reg = <0>;
			width = <240>;
			height = <240>;
			x-offset = <0>;
			y-offset = <0>;
			vcom = <0x32>;//vcom = <0x19>;
			gctrl = <0x35>;
			vrhs = <0x15>;//vrhs = <0x12>;
			vdvs = <0x20>;
			mdac = <0x00>;//方向
			gamma = <0x01>;
			colmod = <0x05>;
			lcm = <0x2c>;
			porch-param = [0c 0c 00 33 33];
			cmd2en-param = [5a 69 02 01];
			pwctrl1-param = [a4 a1];
			pvgam-param = [d0 08 0e 09 09 05 31 33 48 17 14 15 31 34];
			nvgam-param = [d0 08 0e 09 09 15 31 33 48 17 14 15 31 34];
			ram-param = [00 F8];
			rgb-param = [CD 08 14];
			mipi-mode = "MIPI_DBI_MODE_SPI_4WIRE";//MIPI_DBI_MODE_SPI_3WIRE


// lcm:
// gamma:
// cmd2en-param:
// ram-param:
// rgb-param:
        };
    };

    leds {
        compatible = "gpio-leds";
        awaken: led_0 {
            gpios = <&gpio2 9 GPIO_ACTIVE_HIGH>;
        };
    };


	
    buttons {
    	compatible = "gpio-keys";
    	button0: button_0 {
    		gpios = <&gpio1 13 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>;
    		zephyr,code = <INPUT_KEY_0>;
   		};
   		button2: button_2 {
			gpios = <&gpio1 8 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>;
			label = "Push button 2";
			zephyr,code = <INPUT_KEY_2>;
		};

		};

		pwmleds {
			compatible = "pwm-leds";
			pwmled1: pwm_led_1 {
				pwms = <&pwm20 0 PWM_MSEC(20) PWM_POLARITY_INVERTED>;
			};
		};

  	aliases {
        awaken = &awaken;
				sw0 = &button0;
				sw2 = &button2;
				watchdog0 = &wdt31;
				oledluminance = &pwmled1;
    };
			

};

&pwm20 {
	status = "okay";
	pinctrl-0 = <&pwm20_default>;
	pinctrl-1 = <&pwm20_sleep>;
	pinctrl-names = "default", "sleep";
};

&pinctrl {
	spi00_default: spi00_default {
		group1 {
			psels = <NRF_PSEL(SPIM_SCK, 2, 1)>,
				<NRF_PSEL(SPIM_MOSI, 2, 2)>,
				<NRF_PSEL(SPIM_MISO, 2, 4)>;
		};
	};

	spi00_sleep: spi00_sleep {
		group1 {
			psels = <NRF_PSEL(SPIM_SCK, 2, 1)>,
				<NRF_PSEL(SPIM_MOSI, 2, 2)>,
				<NRF_PSEL(SPIM_MISO, 2, 4)>;
				low-power-enable;
		};
	};

    uart21_default: uart21_default {
        group1 {
            psels = <NRF_PSEL(UART_TX, 1, 6)>;
        };

        group2 {
            psels = <NRF_PSEL(UART_RX, 1, 7)>;
            bias-pull-up;
        };
    };

	pwm20_default: pwm20_default {
        group1 {
            psels = <NRF_PSEL(PWM_OUT0, 1, 10)>;
            nordic,invert;
        };
    };

	pwm20_sleep: pwm20_sleep {
			group1 {
					psels = <NRF_PSEL(PWM_OUT0, 1, 10)>;
					low-power-enable;
			};
	};
};

&spi00 {
	compatible = "nordic,nrf-spim";
	status = "okay";
	pinctrl-0 = <&spi00_default>;
	pinctrl-1 = <&spi00_sleep>;
    pinctrl-names = "default","sleep";
	cs-gpios = <&gpio2 10 GPIO_ACTIVE_LOW>;
};

&mx25r64 { 
    status = "disabled";
};

&uart21 {
    status = "okay";
    pinctrl-0 = <&uart21_default>;
    pinctrl-names = "default";
    compatible = "nordic,nrf-uarte";
    current-speed = <115200>;
};


&wdt31 {
	status = "okay";
};




