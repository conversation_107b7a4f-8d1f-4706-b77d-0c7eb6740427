#ifndef __BLE_DEF_GLOBAL__
#define __BLE_DEF_GLOBAL__  // BLE业务部分相关的全局配置

#define BLE_GAP_APPEARANCE_GENERIC_TAG 0x0200
#define BLE_GAP_URI_PREFIX_HTTPS 0x17
#define BLE_GAP_LE_ROLE_PERIPHERAL 0x00

#define BLE_ATT_PREFERED_MTU CONFIG_BT_NIMBLE_ATT_PREFERRED_MTU

#define NIMBLE_PRIORITIES_HOST_TASK 5
#define NIMBLE_SIZE_HOST_TASK_STACK 4096

# define INTVL_CONN_MAX_ST  50*100/125  // 接受的最大连接间隔step数
# define INTVL_CONN_MIN_ST  8*100/125   // 接受的最小连接间隔step数
# define TMOUT_CONN_MAX_10  70          // 接受的连接超时时长, 单位10ms
# define TMOUT_CONN_LATENC  10          // 接受的最多错过连接间隔数
// 尝试指定上位机的连接间隔, 主要是为了确保OTA速度符合预期


    




#endif  // __BLE_DEF_GLOBAL__