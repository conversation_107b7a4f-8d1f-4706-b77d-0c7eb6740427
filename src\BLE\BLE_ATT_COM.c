/**
 * @file BLE_COM.h
 * @brief 蓝牙ATT通信数据处理
 *
 * 处理手机APP上下发的对应蓝牙ATT指令
 *
 * @date 2025-06-23
 * <AUTHOR>
 */
#include "BLE_ATT_COM.h"

#include <zephyr/sys_clock.h>
#include <zephyr/kernel.h>
#include <string.h>
#include <stdlib.h>

#include "central_and_peripheral.h"
#include "Flash_Control.h"
#include "System_Config.h"
#include "Game_Common.h"

#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(ATT, LOG_LEVEL_DBG);

ST_Ble_MACList BLE_List_BlackList = {0};    // 黑名单列表
ST_Ble_MACList BLE_List_Validity = {0};     // 合法性列表
ST_Ble_MACList BLE_List_Offline = {0};      // 离线设备列表
// ST_Ble_MACList BLE_List_WhiteList = {0};    // 白名单列表 用于记录保存在flash内的信息


#define PPL_WTRIE_TIMEOUT_MS (uint8_t)(10)    // 10毫秒一次
struct k_work_delayable ppl_write_work;      // 延时工作队列，用PPL发送数据队列
#define SCAN_TIMEOUT_S (uint8_t)(15)            // 15秒超时
struct k_work_delayable scan_timeout_work;      // 延时工作队列，用于处理扫描超时
#define ATT_LOOP_TIME_MS (uint8_t)(100) // 循环执行工作队列 100mS一次
struct k_work_delayable att_loop_work;  // 循环执行工作队列
extern Flash_Control_manager_t DG_LAB_FLASH;

struct k_msgq Queue_PPL_WrIte;
static  uint8_t Queue_PPL_WrIte_buffer[GAME_EVENT_ID_MAX*20];
/* 动态初始化消息队列 */
static void Queue_PPL_WrIte_Init(void)
{
  k_msgq_init(&Queue_PPL_WrIte, Queue_PPL_WrIte_buffer, 20, GAME_EVENT_ID_MAX);
}

/// @brief 大小端转换
static int16_t swap_int16(int16_t val)
{
  uint16_t uval = (uint16_t)val;
  uval          = (uval >> 8) | (uval << 8);
  return (int16_t)uval;
}

/// @brief 映射
uint16_t map(uint16_t x, uint16_t in_min, uint16_t in_max, uint16_t out_min, uint16_t out_max) 
{
  return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

/// @brief 获取随机数
/// @param return_max 返回随机数的最大值
/// @return  随机数
static uint16_t Get_Random_Nunber(uint16_t return_max)
{
  uint16_t random = (uint16_t)k_uptime_get_32();  // 取低位时间变化 用于随机
  random ^= random << 3;
  random ^= random >> 5;
  random ^= random << 2;
  return  map(random, 0, 0xFFFF, 0, return_max);  // 映射到总时长内
}

/// @brief 增加一个值在数组后面
/// @param arr 数组指针
/// @param value 要增加的值
/// @return 0 成功，-1 失败
int BLE_ATT_Array_store_first_zero(uint8_t *arr, uint8_t value)
{
  for (uint8_t i = 0; i < BLE_CTL_Device_Connect_MAX; i++)
  {
    if (arr[i] == 0)
    {
      arr[i] = value;
      return 0;
    }
  }
  return -1;
}

/// @brief 删除数组中的第一个元素，并将后续元素前移
void BLE_ATT_Array_remove_first_and_shift(uint8_t *arr)
{
  for (uint8_t i = 1; i < BLE_CTL_Device_Connect_MAX; i++)
  {
    arr[i - 1] = arr[i];
  }
  arr[BLE_CTL_Device_Connect_MAX - 1] = 0;
}

/**
 * @brief 检查uint8_t变量中第一个为0的位，并返回其位置（从低到高，0~7），若全为1返回0xFF
 * @param value 要检查的uint8_t值
 * @return 第一个为0的位的位置（0~7），若全为1返回0xFF
 */
uint8_t BLE_ATT_Find_First_Zero_Bit(uint8_t value)
{
  for (uint8_t i = 0; i < 8; i++) 
  {
    if (((value >> i) & 0x01) == 0) 
    {
      return i;
    }
  }
  return 0xFF;
}

/**
 * @brief 检查数组ST_CTL_FLASH eSubDevice_Color与fid值相等
 * @param *info_array 参考 ST_CTL_FLASH
 * @param id 要比较的id值
 * @return 返回相等值的位置，没查到返回-1
 */
static int8_t BLE_ATT_Find_Flash_ID_EQ(ST_CTL_FLASH *info_array, uint8_t id)
{
  for (uint8_t i = 0; i < BLE_CTL_Device_Connect_MAX; i++) 
  {
    if(info_array->INFO_Array[i].Type_Data.Button.eSubDevice_Color == id)
    {
      return i;
    }
  }
  return -1;
} 

void LOG_MAC(bt_addr_le_t* _mac)
{
  char mac_str[BT_ADDR_LE_STR_LEN];
  bt_addr_le_to_str(_mac, mac_str, sizeof(mac_str));
  LOG_INF("MAC: %s", mac_str);
}

/**
 * @brief 比较两个bt_addr_le_t类型的MAC地址是否相同
 * @param mac1 第一个MAC地址指针
 * @param mac2 第二个MAC地址指针
 * @return true 相同，false 不同
 */
static uint8_t bt_addr_le_equal(const bt_addr_le_t *mac1, const bt_addr_le_t *mac2)
{
  return (memcmp(mac1->a.val, mac2->a.val, sizeof(mac1->a.val)) == 0);
}

/**
 * @brief 检查CTL_CONN mac与输入MAC相同
 * @param *mac 参考 bt_addr_le_t
 * @return 返回相等值的位置，没查到返回-1
 */
static int8_t Get_CTL_CONN_Index(const bt_addr_le_t *mac)
{
  for (int i = 0; i < CONFIG_BT_MAX_CONN; i++) 
  {
    if (bt_addr_le_equal(&BLE_INFO.CTL_CONN[i].mac, mac)) 
    {
      return i;
    }
  }
  return -1;
}

/**
 * @brief 通过子设备ID获取索引
 * @param subDevice 子设备ID
 * @return 返回CTL Index ，错误返回-1 没找到返回 -2
 */
int8_t BLE_ATT_Get_CTL_CONN_Index_Fo_SubDevice(uint8_t subDevice)
{
  int8_t _flashID_Buff=BLE_ATT_Find_Flash_ID_EQ(&BLE_INFO.CTL_FLASH,subDevice);
  if(_flashID_Buff == -1)
  { 
    // BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_OBJ_None_u8, 1);  // 错误 回复0x04
    return -1;
  }
  for(uint8_t conn_idex =0;conn_idex< BLE_CTL_Device_Connect_MAX;conn_idex++)//通过ID查MAC
  {
    if(BLE_INFO.CTL_FLASH.INFO_Array[conn_idex].Flash_ID_Num == (eFLASH_BLE_Device_ID_START+_flashID_Buff))//ID相同
    {
      conn_idex = Get_CTL_CONN_Index(&BLE_INFO.CTL_FLASH.INFO_Array[conn_idex].MAC);//查索引位置
      return (int8_t)conn_idex;
    }
  }
  return -2;
}

/// @brief 从flash上更新白名单列表
/// @return true:成功 false:失败
uint8_t Updata_Flash_From_WhiteList(void)
{
  // char Flash_WhiteList_Buff[BT_ADDR_LE_STR_LEN];//白名单数据缓存区
  // BLE_MACList_Clear(&BLE_List_WhiteList);//先清空白名单
  BLE_INFO.CTL_FLASH.Valid_Device_BitFLag=0;//清空先
  BLE_INFO.CTL_FLASH.Valid_Count=0;//清空先
  for(uint8_t num_ID=0;num_ID<eFLASH_BLE_Device_ID_END-eFLASH_BLE_Device_ID_START;num_ID++)//把连接过的全部读出来
  { 
    if(Flash_Control_read(&DG_LAB_FLASH, eFLASH_BLE_Device_ID_START+num_ID, &BLE_INFO.CTL_FLASH.INFO_Array[num_ID], sizeof(BLE_INFO.CTL_FLASH.INFO_Array[num_ID])) > 0)//读falsh
    {
      BLE_INFO.CTL_FLASH.Valid_Count++;//有效数据计数器加1
      BLE_INFO.CTL_FLASH.Valid_Device_BitFLag |= (1<<num_ID); // 标记为有效设备
      // LOG_DBG("READ MAC %d:%s -END-",num_ID,&BLE_INFO.CTL_FLASH.INFO_Array[num_ID].MAC);
      // LOG_WRN("READ id:%d",eFLASH_BLE_Device_ID_START+num_ID);
      BLE_INFO.CTL_FLASH.INFO_Array[num_ID].Flash_ID_Num = (eFLASH_BLE_Device_ID_START+num_ID);
      char mac_str[BT_ADDR_LE_STR_LEN];
      bt_addr_le_to_str(&BLE_INFO.CTL_FLASH.INFO_Array[num_ID].MAC, mac_str, sizeof(mac_str));
      // LOG_DBG("MAC: %s", mac_str);
      // LOG_DBG("ID:%d,type:%d,LEN:%d,qeuid:%d", BLE_INFO.CTL_FLASH.INFO_Array[num_ID].Flash_ID_Num, BLE_INFO.CTL_FLASH.INFO_Array[num_ID].Device_Type, BLE_INFO.CTL_FLASH.INFO_Array[num_ID].Device_Type_Data_LEN,
              // BLE_INFO.CTL_FLASH.INFO_Array[num_ID].Type_Data.Button.eSubDevice_Color);
    }
    else//错误 或者没数据 
    {
      BLE_INFO.CTL_FLASH.INFO_Array[num_ID].Flash_ID_Num = 0;//这个位置空出来
    //   LOG_DBG("FLASH NULL %d",num_ID);
    //   //不处理  因为可能解绑了中间的设备信息
    }
  }
  return true;
}

/// @brief 从flash上更新玩法触列表
/// @return true:成功 false:失败
static void Updata_Flash_From_GameEvent(void)
{
  for(uint8_t num_ID=0;num_ID<eFLASH_Game_EVENT_ID_END-eFLASH_Game_EVENT_ID_START;num_ID++)//全部读出来
  { 
    if(Flash_Control_read(&DG_LAB_FLASH, eFLASH_Game_EVENT_ID_START+num_ID, GAME_INFO.Game_EVENT.Config[num_ID].All_Arrays, sizeof(GAME_INFO.Game_EVENT.Config[num_ID].All_Arrays)) > 0)//读falsh
    {
      GAME_INFO.Game_EVENT.Count++;
      GAME_INFO.Game_EVENT.ID_BitFLag |= (1<<num_ID);
      LOG_HEXDUMP_WRN(GAME_INFO.Game_EVENT.Config[num_ID].All_Arrays,19,"Game_EVENT Flash Data");
      k_sleep(K_MSEC(10));
    }
  }
}

/// @brief 定义中央设备通知回调函数类型
static void CTL_Receive_CB(struct bt_conn *conn, const void *data, uint16_t length)
{
  uint8_t *data_buff = (uint8_t *)data;
  // uint8_t conn_index=BLE_INFO.CTL_CONF.Index_MAP[bt_conn_index(conn)];
  // LOG_DBG("Receive %d", conn_index);
  uint8_t index_valodiity = 0;
  // uint8_t conn_index=0;
  const bt_addr_le_t *pmac_buff=bt_conn_get_dst(conn);
  if(BLE_MACList_Check(&BLE_List_Validity,pmac_buff) == true)//这个设备还没通过合法性验证
  {
    // LOG_DBG("Validity_BitFlag");
    for(;index_valodiity<CONFIG_BT_MAX_CONN;index_valodiity++)
    {
      if(bt_addr_le_equal(&BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].MAC,pmac_buff) == true)//这个MAC在这
      {
        break;
      }
    }
    uint8_t Color_ID = BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Data[1];
    uint8_t _Flash_ID = Color_ID-1;
    LOG_DBG("data:%x,[%d]%d == %d",*data_buff, index_valodiity,Color_ID,(data_buff[1]));
    LOG_MAC(pmac_buff);
    if (*data_buff == Reply_CMD_HEADER_RSP && Color_ID == (data_buff[1]))// 响应了验证指令 0x51 && ID是否一致
    {
      BLE_INFO.CTL_FLASH.INFO_Array[data_buff[1]].Type_Data.Button.SOC = data_buff[3];//记录SOC
      if (BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Device_NEW_Flag == true)// 新设备接入 先写入flash 再回复验证通过
      {
        LOG_DBG("New Device Bind");
        // for (uint8_t _Flash_ID = 0; _Flash_ID < (eFLASH_BLE_Device_ID_END - eFLASH_BLE_Device_ID_START); _Flash_ID++)  // 找一个空的ID出来 写Flash
        // {
          if (BLE_INFO.CTL_FLASH.INFO_Array[_Flash_ID].Flash_ID_Num == 0)  // ID可用
          {
            BLE_INFO.CTL_FLASH.INFO_Array[_Flash_ID].Flash_ID_Num = _Flash_ID+eFLASH_BLE_Device_ID_START;        // 写入FLASH ID
            BLE_INFO.CTL_FLASH.INFO_Array[_Flash_ID].Device_Type  = *(data_buff + 2);                            // 设备类型
            memcpy(&BLE_INFO.CTL_FLASH.INFO_Array[_Flash_ID].MAC, pmac_buff, sizeof(bt_addr_le_t));  // 记录MAC
            BLE_INFO.CTL_FLASH.INFO_Array[_Flash_ID].Type_Data.Button.eSubDevice_Color = Color_ID; // 分配颜色
            BLE_INFO.CTL_FLASH.INFO_Array[_Flash_ID].Device_Type_Data_LEN = 1;                                   // 没有玩法 只有一个颜色数据
            Flash_Control_write(&DG_LAB_FLASH, eFLASH_BLE_Device_ID_START + _Flash_ID, &BLE_INFO.CTL_FLASH.INFO_Array[_Flash_ID],
                                sizeof(BLE_INFO.CTL_FLASH.INFO_Array[_Flash_ID]));
                                
            LOG_WRN("NEW FLASH id:%d,type:%d,color:%d", 
              BLE_INFO.CTL_FLASH.INFO_Array[_Flash_ID].Flash_ID_Num,
              BLE_INFO.CTL_FLASH.INFO_Array[_Flash_ID].Device_Type,
              BLE_INFO.CTL_FLASH.INFO_Array[_Flash_ID].Type_Data.Button.eSubDevice_Color);
              LOG_MAC(&BLE_INFO.CTL_FLASH.INFO_Array[_Flash_ID].MAC);
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Bind_New_Equipment_Array(*(data_buff + 1), *(data_buff + 2), *(data_buff + 3), 1), 5);  // 回复0x02
            // break;
          }
          else
          {
            LOG_ERR("FLASH ID ERROR");
          }
        // }
      }
      else//旧设备
      {
        LOG_DBG("Old Device Bind");
        // 回复0x04→ 扫描并且成功绑定到了之前已经绑定过的配件
        BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX,Reply_Bind_Old_Equipment_Array(*(data_buff +1),*(data_buff +2),*(data_buff +3),1),5); //回复
      }
      BLE_MACList_Remove(&BLE_List_Validity, bt_conn_get_dst(conn));//删除MAC
    }
    else
    {
      //只再重发一遍验证 
      LOG_DBG("Again Send");
      BLE_CTL_Write(conn,BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Data,sizeof(BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Data));
      BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].SendNum_Count++;
      if(BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].SendNum_Count >= 5)
      {
        BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].SendNum_Count=0;
        BLE_MACList_Remove(&BLE_List_Validity, bt_conn_get_dst(conn));
      }
    }
    
  }
  else//通过了验证
  {
    LOG_HEXDUMP_DBG(data_buff, length, "CTL data:");
    LOG_MAC((const bt_addr_le_t*)bt_conn_get_dst(conn));
    // BLE_INFO.CTL_FLASH.INFO_Array[data_buff[1]].Type_Data.Button.SOC = data_buff[3];//记录SOC
    switch (data_buff[0])
    {
      case Receive_Game_CTL_SubDevice_State:  // 回复0x51→子设备返回自身状态
      {
        
      }
      break;
      case Receive_Game_CTL_Colour_SN:  // 回复0x53→作为从机时，连接上之后自动发送自己的颜色，以及SN号
      {

      }
      break;
      case Receive_Game_Event_Trigger:  // 回复0x5A→事件触发
      {
        uint8_t event_ID = data_buff[2];//事件ID
        if(event_ID>0) event_ID--;
        // if(data_buff[3] == true)//触发 有的设备触发也是0
        {
          if(GAME_INFO.Game_State == eUndeway)//玩法进行中
          { 
            //-----------------------------------------一次性结果 触发结果--------------------------------------
            //-----------APP:永久强度改变 模式开锁概率------------------
            if(GAME_INFO.Game_EVENT.Config[event_ID].Data.Strength_A != 0)//开锁几率（固定值）
            {
              int8_t percentage = GAME_INFO.Game_EVENT.Config[event_ID].Data.Strength_A;
              (percentage < 0) ? (percentage = 10 - abs(percentage)) : (percentage += 10);  // 适应APP值-10~+10 ！！！!!!
              uint16_t random = (uint16_t)k_uptime_get_32();                                // 取低位时间变化 用于随机
              random ^= random << 3;
              random ^= random >> 5;
              random ^= random << 2;
              uint16_t value = 65535 / (percentage + 1);  // 0不能被除所以+1
              LOG_DBG("random :%d percentage %d", random, value);
              if (random < value)  // 开锁判定
              {
                LOG_DBG("OPEN lock");
                // GAME_INFO.Game_Play.Time_Over =0;
              }
              // else  // 开锁失败了 剩余时间一次性变化
              // {
              //   int8_t Strength_B      = GAME_INFO.Game_EVENT.Config[event_ID].Data.Strength_B;
              //   int8_t Strength_B_Last = GAME_INFO.Game_EVENT.Config[event_ID].Data.Strength_B_Last;
              //   if (Strength_B == Strength_B_Last)  // 固定时间 固定变化
              //   {
              //     LOG_DBG("Temp_Strength B:%d",Strength_B);
              //     if(Strength_B > 0)//加时间
              //     {
              //       if((GAME_INFO.Game_Play.Time_Over+ Strength_B) >= GAME_INFO.Game_Play.Time_Remaining)//不能大于总游戏时间
              //       {
              //         GAME_INFO.Game_Play.Time_Over = GAME_INFO.Game_Play.Time_Remaining;
              //       }
              //       else
              //       {
              //         GAME_INFO.Game_Play.Time_Over += Strength_B;
              //       }
              //     }
              //     else
              //     {
              //       (GAME_INFO.Game_Play.Time_Over > abs(Strength_B)) ? (GAME_INFO.Game_Play.Time_Over -= abs(Strength_B)) : (GAME_INFO.Game_Play.Time_Over = 0);
              //     }
              //   }
              //   else  // 随机时间 随机范围
              //   {
              //     uint8_t abs_Strength_B_Last = abs(Strength_B_Last);
              //     uint16_t _Temp_StrengthACC       = Strength_B + abs_Strength_B_Last;  // 总时长
              //     uint16_t map_Temp_StrengthACC    = Get_Random_Nunber(_Temp_StrengthACC);         // 映射到总时长内
              //     LOG_DBG("Temp_Strength:%d", map_Temp_StrengthACC);
              //     if (map_Temp_StrengthACC >= abs_Strength_B_Last)  // 加时间
              //     {
              //       map_Temp_StrengthACC -= abs_Strength_B_Last;
              //       ((GAME_INFO.Game_Play.Time_Over + map_Temp_StrengthACC) >= GAME_INFO.Game_Play.Time_Remaining) ? (GAME_INFO.Game_Play.Time_Over = GAME_INFO.Game_Play.Time_Remaining)
              //                                                                                                      : (GAME_INFO.Game_Play.Time_Over += map_Temp_StrengthACC);
              //     }
              //     else
              //     {
              //       map_Temp_StrengthACC = abs_Strength_B_Last - map_Temp_StrengthACC;
              //       LOG_DBG("DEC:%d", map_Temp_StrengthACC);
              //       (GAME_INFO.Game_Play.Time_Over > map_Temp_StrengthACC) ? (GAME_INFO.Game_Play.Time_Over -= map_Temp_StrengthACC) : (GAME_INFO.Game_Play.Time_Over = 0);
              //     }
              //   }
              //   if(GAME_INFO.Game_Play.Time_Over==0)
              //   {
              //     GAME_INFO.Game_State = eInitial;
              //     BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State, 0), 4);
              //     LOG_DBG("Time_Over1:%d",GAME_INFO.Game_Play.Time_Over);
              //   }
              //   else
              //   {
              //     BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State,GAME_INFO.Game_Play.Time_Over), 4);
              //   }
              // }
            }
            //-----------APP:剩余游戏时间改变------------------
            if(GAME_INFO.Game_EVENT.Config[event_ID].Data.GameTime != 0 && GAME_INFO.Game_Play.Time_Over > 0)//设置了时间  && 还没开锁
            {
              int16_t _GameTime = swap_int16(GAME_INFO.Game_EVENT.Config[event_ID].Data.GameTime);
              int16_t _GameTime_Last = swap_int16(GAME_INFO.Game_EVENT.Config[event_ID].Data.GameTime_Last);
              if(_GameTime == _GameTime_Last)//固定时间变化
              {
                if(_GameTime > 0)//加时间
                {
                  if((GAME_INFO.Game_Play.Time_Over+ _GameTime) >= GAME_INFO.Game_Play.Time_Remaining)//不能大于总游戏时间
                  {
                    GAME_INFO.Game_Play.Time_Over = GAME_INFO.Game_Play.Time_Remaining;
                  }
                  else 
                  {
                    GAME_INFO.Game_Play.Time_Over += _GameTime;
                  }
                }
                else
                {
                  (GAME_INFO.Game_Play.Time_Over > abs(_GameTime)) ? (GAME_INFO.Game_Play.Time_Over -= abs(_GameTime)):(GAME_INFO.Game_Play.Time_Over=0);
                }
              }
              else//随机变化
              {
                if(GAME_INFO.Game_Play.Time_Over > 0)//=0 说明没开始或者解锁了
                {
                  uint16_t abs_GameTime_Last = abs(_GameTime_Last);
                  uint16_t _GameTimeACC = _GameTime + abs_GameTime_Last;//总时长
                  uint16_t map_GameTimeACC = Get_Random_Nunber(_GameTimeACC);//映射到总时长内
                  LOG_DBG("random 1:%d",map_GameTimeACC);
                  if(map_GameTimeACC >= abs_GameTime_Last)//加时间
                  {
                    map_GameTimeACC -= abs_GameTime_Last;
                    LOG_DBG("ACC:%d",map_GameTimeACC);
                    if((GAME_INFO.Game_Play.Time_Over+map_GameTimeACC) >= GAME_INFO.Game_Play.Time_Remaining)//不能大于总游戏时间
                    {
                      GAME_INFO.Game_Play.Time_Over = GAME_INFO.Game_Play.Time_Remaining;
                    }
                    else
                    {
                      GAME_INFO.Game_Play.Time_Over += map_GameTimeACC;
                    }
                  }
                  else
                  {
                    map_GameTimeACC = abs_GameTime_Last - map_GameTimeACC ;
                    LOG_DBG("DEC:%d",map_GameTimeACC);
                    (GAME_INFO.Game_Play.Time_Over > map_GameTimeACC) ? (GAME_INFO.Game_Play.Time_Over -= map_GameTimeACC):(GAME_INFO.Game_Play.Time_Over=0);
                  }
                }
                LOG_DBG("Time_Over:%d",GAME_INFO.Game_Play.Time_Over);
              }
              LOG_DBG("timeA:%d B:%d",_GameTime,_GameTime_Last);
              if(GAME_INFO.Game_Play.Time_Over==0)
              {
                GAME_INFO.Game_State = eInitial;
                BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State, 0), 4);
              }
              else
              {
                BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State,GAME_INFO.Game_Play.Time_Over), 4);
              }
            }
            //-----------APP:立刻停止游戏------------------
            if(GAME_INFO.Game_EVENT.Config[event_ID].Data.Skip_Delay & (0x01<<0))//停止(1<<0) 
            {
              GAME_INFO.Game_Play.Time_Over =0;
              LOG_DBG("Stop Game:0");
            }
            //-----------APP:立刻跳过延迟开始游戏-----------------
            if(GAME_INFO.Game_EVENT.Config[event_ID].Data.Skip_Delay & (0x01<<1))//跳过延迟开始游戏(1<<1)
            {
              GAME_INFO.Game_Play.Time_Delay_Game_Start =0;
               LOG_DBG("jump Game:1");
            }

            //-----------------------------------------持续性结果 APP:以下为临时触发结果--------------------------------------
            //----------- APP:临时输出波形 A通道：内置模式1-----------------
            if(GAME_INFO.Game_EVENT.Config[event_ID].Data.COM_A_EN == 0x01)//触发期间剩余时间停止倒计时
            {
              GAME_INFO.Game_Play.Time_Over_Stop_Falg = true;//停止计数
              LOG_DBG("Time_Over_Stop_Falg:1");
            }
            //-----------APP:强度临时改变 仅A-----------------
            if(GAME_INFO.Game_EVENT.Config[event_ID].Data.Temp_Strength_A_Way == eAddition)
            {
              GAME_INFO.Game_Play.Time_Over_Mode = eAddition;
            }
            else if(GAME_INFO.Game_EVENT.Config[event_ID].Data.Temp_Strength_A_Way == eAddition_Parameter)
            {
              GAME_INFO.Game_Play.Time_Over_Mode = eAddition_Parameter;
            }
            GAME_INFO.Game_Play.Time_Over_Addition_Parameter =  GAME_INFO.Game_EVENT.Config[event_ID].Data.Temp_Strength_A;
          }
        }
        //----------------------处理巡回犬模式触发----------------------------------
        if(GAME_INFO.Game_Play.Hover_Dog.Start_EN == true &&  GAME_INFO.Game_Play.Hover_Dog.Mode_Start_Flag == true)//使能 && 巡回犬玩法开启
        {
          if(GAME_INFO.Game_Play.Hover_Dog.Triger_ID == data_buff[1])//触发值==触发设备ID
          {
            GAME_INFO.Game_Play.Hover_Dog.Triger_ID_Type = eSubDevice;//子设备触发
          }
        }
      }
      break;
      case Receive_Game_Event_Cancel:  // 回复0x5B→触发取消
      {
        uint8_t event_ID = data_buff[2];//事件ID
        if(event_ID>0) event_ID--;
        if(GAME_INFO.Game_EVENT.Config[event_ID].Data.COM_A_EN == 0x01)//取消了 触发期间剩余时间停止倒计时
        {
          GAME_INFO.Game_Play.Time_Over_Stop_Falg = false;//恢复计数
          LOG_DBG("Time_Over_Stop_Falg:0");
        }
        if(GAME_INFO.Game_EVENT.Config[event_ID].Data.Temp_Strength_A_Way != 0)
        {
          GAME_INFO.Game_Play.Time_Over_Mode = eTime_Over_Init;
          LOG_DBG("eTime_Over_Init");
        }
        
      }
      case Receive_Game_Updata:  // 回复0x5C→参数更新
      {
        
      }
      break;
      default:
        break;
    }
  }
}



/// @brief 循环移动bt_addr_le_t类型的数组，将[0]数据移动到第一个空数据位置，如果没有空位则整体循环移位
void BLE_ATT_bt_addr_le_array_shift(bt_addr_le_t *arr)
{
  bt_addr_le_t tmp = arr[0];
  for (uint8_t i = 0; i < BLE_CTL_Device_Connect_MAX - 1; i++)
  {
    arr[i] = arr[i + 1];// 数据前移
  }
  int empty_idx = -1;
  for (uint8_t i = 0; i < BLE_CTL_Device_Connect_MAX; i++)// 查找空位置
  {
    uint8_t is_empty = 1;
    for (uint8_t j = 0; j < sizeof(arr[i].a.val); j++)
    {
      if (arr[i].a.val[j] != 0)
      {
        is_empty = 0;
        break;
      }
    }
    if (is_empty)
    {
      empty_idx = i;
      break;
    }
  }
  if (empty_idx != -1)
  {
    arr[empty_idx] = tmp;
  }
  else
  {
    arr[BLE_CTL_Device_Connect_MAX - 1] = tmp;
  }
}

/// @brief 检查离线设备并将其MAC地址存入Offline_Device_Queue队列
static void Update_Offline_Device_Queue(void)
{
  memset(&BLE_INFO.CTL_CONF.OFFLINE, 0, sizeof(BLE_INFO.CTL_CONF.OFFLINE));  // 清空数据缓存区
  // LOG_DBG("Valid_Count:%d",BLE_INFO.CTL_FLASH.Valid_Count);
  Updata_Flash_From_WhiteList();//更新最新的数据
  // k_sleep(K_MSEC(100)); // 延迟100ms
  for (uint8_t i = 0; i < eFLASH_BLE_Device_ID_END-eFLASH_BLE_Device_ID_START; i++)
  {
    if (!(BLE_INFO.CTL_FLASH.Valid_Device_BitFLag & (1 << i)))// 跳过无效设备
    {
      continue;
    }
    int8_t conn_idex = Get_CTL_CONN_Index(&BLE_INFO.CTL_FLASH.INFO_Array[i].MAC);//查索引位置
    if (conn_idex == -1)  //在线没找到
    {
      // k_msgq_put(&Offline_Device_Queue, &BLE_INFO.CTL_FLASH.INFO_Array[i].MAC, K_NO_WAIT);// 离线，加入队列
      if(BLE_MACList_Check(&BLE_List_Offline,&BLE_INFO.CTL_FLASH.INFO_Array[i].MAC) == false)//不存在
      {
        BLE_MACList_Add(&BLE_List_Offline,&BLE_INFO.CTL_FLASH.INFO_Array[i].MAC);  //加入离线设备
        LOG_DBG("ADD MAC,list:%d",BLE_MACList_Get_Count(&BLE_List_Offline));
        LOG_MAC(&BLE_INFO.CTL_FLASH.INFO_Array[i].MAC);
      }
      // uint8_t  count_num = BLE_INFO.CTL_CONF.OFFLINE.Count_Num_Max;
      // BLE_INFO.CTL_CONF.OFFLINE.MAC[count_num] = BLE_INFO.CTL_FLASH.INFO_Array[i].MAC;
      // BLE_INFO.CTL_CONF.OFFLINE.Count_Num_Max++;
    }
  }
  // uint8_t offline_count = BLE_MACList_Get_Count(&BLE_List_Offline);
  // LOG_INF("Offline device count: %d", offline_count);
  // for (uint8_t i = 0; i < offline_count; i++)
  // {
  //   bt_addr_le_t mac;
  //   BLE_MACList_Get_At(&BLE_List_Offline, i, &mac);
  //   LOG_MAC(&mac);
  //   k_sleep(K_MSEC(1));
  // }
  BLE_INFO.CTL_CONF.OFFLINE.Await_Updata_Flag = false;//数据更新完成
}



/// @brief  CTL扫描超时处理函数
static void scan_timeout_handler(struct k_work *work)
{
  LOG_INF("Scan timeout reached, stopping BLE scan");
  BLE_INFO.CTL_CONF.Scan_Mode = eNone_SCAN;   //无扫描模式
  bt_le_scan_stop();//停止扫描
  BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX,Reply_Bind_Scan_Time_Out_u8,1); //回复0x03
  // BLE_INFO.CTL_CONF.Auto_SCAN_Flag = true;
} 

/// @brief  接收到ATT写入数据回调函数
static void PPL_Receive_CB(ePPL_CB_Type_t _type, const uint8_t *const _data, uint16_t *_len)
{
  int rc;
  uint8_t start_queue_flag = false;  // 启动队列标志
  uint8_t *data_buff = (uint8_t *)_data;
  LOG_DBG("Received data type: %d, length: %d", _type, *_len);
  switch (_type)
  {
    case ePPL_CB_TYPE_Write_OMS_RX:  // 上位机在0x150A特征写入数据
    {
      //   const uint8_t *data="OMS";
      //   BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX,data);  //测试发送函数
      switch (*data_buff)
      {
        case Receive_ATT_SCAN:  // 写入0x01
        {
          LOG_INF("Received ATT_SCAN command");
          if (BLE_INFO.CTL_CONF.conn_count < BLE_CTL_Device_Connect_MAX)  // 当前连接数小于最大连接数才处理
          {
            BLE_INFO.CTL_CONF.Disconn_Scan_EN_Flag = true;   // 开启断开设备后的扫描
            if (BLE_MACList_Get_Count(&BLE_List_BlackList))  // 黑名单有数据
            {
              BLE_MACList_Clear(&BLE_List_BlackList);  // 清空黑名单
            }
            // k_work_cancel_delayable(&scan_timeout_work);//取消扫描超时函数
            k_work_schedule(&scan_timeout_work, K_SECONDS(SCAN_TIMEOUT_S));
            BLE_INFO.CTL_CONF.Scan_Mode = eActive_SCAN;  // 主动扫描模式
            BLE_CTL_LE_start_scan_begin();               // 启动BLE扫描
          }
          else
          {
            LOG_WRN("Maximum connections reached, cannot start scan");
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Bind_Scan_Time_Out_u8, 1);  // 回复0x03
          }
        }
        break;
        case Receive_ATT_UNBIND_COLOR:  // 解绑指定颜色配件 0X08
        {
          if (data_buff[1] > eSUBDEVICE_COLOR_WHITE && data_buff[1] < eSUBDEVICE_COLOR_YELLOW)  // 错误值
          {
            LOG_ERR("DATA ERROR!!");
            break;
          }
          int8_t _flashID_Buff = BLE_ATT_Find_Flash_ID_EQ(&BLE_INFO.CTL_FLASH, data_buff[1]);  // APP下发数据从1开始算
          if (_flashID_Buff == -1)
          {
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_OBJ_None_u8, 1);  // 错误 回复0x04
            break;// 找不到该颜色配件
          }
          LOG_DBG("UNBIND_COLOR:%d", _flashID_Buff);
          rc = Flash_Control_delete(&DG_LAB_FLASH, eFLASH_BLE_Device_ID_START + _flashID_Buff);  // 删除flash中的存储
          if (rc)
          {
            LOG_ERR("Error while deleting IP address, rc=%d\n", rc);
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_OBJ_None_u8, 1);  // 错误 回复0x04
            return;
          }
          uint8_t conn_idex = 0;
          for (; conn_idex < BLE_CTL_Device_Connect_MAX; conn_idex++)  // 通过ID查MAC
          {
            if (BLE_INFO.CTL_FLASH.INFO_Array[conn_idex].Flash_ID_Num == (eFLASH_BLE_Device_ID_START + _flashID_Buff))  // ID相同
            {
              conn_idex = Get_CTL_CONN_Index(&BLE_INFO.CTL_FLASH.INFO_Array[conn_idex].MAC);            // 查索引位置
              bt_conn_disconnect(BLE_INFO.CTL_CONN[conn_idex].CONN, BT_HCI_ERR_REMOTE_USER_TERM_CONN);  // 断开连接
              break;
            }
          }
          LOG_DBG("bt_conn_disconnect");
          BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Bind_ATT_Unbind_Colo(_flashID_Buff + 1), 2);  // 回复0x09  +1是从1开始算
          Updata_Flash_From_WhiteList();// 删除后更新白名单
        }
        break;
        case SET_GAME_CONFIG:  // 0x50
        {
          // uint8_t data_buff[4]={Reply_SET_GAME_CONFIG,*(_data+1),BLE_Euipment_ID,0x64};
          uint8_t _id_subDviceColor = data_buff[1];  // 配件ID

          BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX,
                        Reply_Set_Game_Config(_id_subDviceColor, 0x03, BLE_INFO.CTL_FLASH.INFO_Array[BLE_ATT_Find_Flash_ID_EQ(&BLE_INFO.CTL_FLASH, _id_subDviceColor)].Type_Data.Button.SOC),
                        Reply_Set_Game_Config_Length);
        }
        break;
        case Receive_ATT_FORWARD:  // 0x60 透传
        {
          int8_t conn_idex = BLE_ATT_Get_CTL_CONN_Index_Fo_SubDevice(data_buff[1]);
          LOG_DBG("0x06 index:%d ", conn_idex);
          LOG_HEXDUMP_INF(data_buff + 2, (*_len) - 2, "");
          if (conn_idex > -1)
          {
            BLE_CTL_Write(BLE_INFO.CTL_CONN[conn_idex].CONN, data_buff + 2, (*_len) - 2);  // 前两个字节不用传
          }
        }
        break;
        case Reply_ATT_CHACK_ALL_SUBDEVICE:  // 0xff
        {
          uint8_t defice_type = false;  // 设备状态
          
          Updata_Flash_From_WhiteList();// 更新白名单

          //0xF1→前文的1，当前配件玩法的工作状态以及剩余时间
          //4字节，0xF1（1字节）+玩法状态（1字节）+剩余时间以秒为单位的整数(2字节) 
          BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State,GAME_INFO.Game_Play.Time_Over), 4);

          // for (uint8_t num = 0; num < eFLASH_Game_EVENT_ID_END - eFLASH_Game_EVENT_ID_START; num++)  // 把有效的全部发上去同步
          // {
          //   if (!(GAME_INFO.Game_EVENT.ID_BitFLag & (1 << num)))  // 跳过无效设备
          //   {
          //     continue;
          //   }
          //   uint8_t ppl_data_buff[20];
          //   ppl_data_buff[0] = 0xF2;  // 0x20
          //   memcpy(&ppl_data_buff[1], GAME_INFO.Game_EVENT.Config[num].All_Arrays, 19);
          //   int ret = BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, ppl_data_buff, 20);  // 发送给APP
          //   LOG_DBG("pRET:%d",ret);
          //   if(ret == -ENOMEM)//没发送成功 放入队列中等待后续发送
          //   {
          //     LOG_HEXDUMP_WRN(ppl_data_buff,20,"MSGQ");
          //     k_msgq_put(&Queue_PPL_WrIte,&ppl_data_buff[0], K_NO_WAIT);//放入队列内
          //     start_queue_flag = true;
          //   }
          //   else
          //   {
          //     LOG_HEXDUMP_DBG(ppl_data_buff, 20, "ppl_data_buff:");
          //   }
          // }
          for (uint8_t num = 0; num < eFLASH_BLE_Device_ID_END - eFLASH_BLE_Device_ID_START; num++)  //0xF3 只查已经找到的数据进行对比
          {
            if (!(BLE_INFO.CTL_FLASH.Valid_Device_BitFLag & (1 << num)))  // 跳过无效设备
            {
              continue;
            }

            // if (bt_addr_le_equal(&BLE_INFO.CTL_CONN[num].mac, &BLE_INFO.CTL_FLASH.INFO_Array[num].MAC) == true)  // 比较两个MAC地址是否相同 说明设备在线
            int8_t conn_idex = Get_CTL_CONN_Index(&BLE_INFO.CTL_FLASH.INFO_Array[num].MAC);  // 查索引位置
            if (conn_idex != -1)
            {
              defice_type = true;  // 在线
            }
            // 每个包20字节，0xF3（1字节）+颜色（1字节）+子设备类型（1字节）+电量百分比（1字节）+连接状态（1字节）+触发设置（15字节）
            uint8_t chack_databuff[20];
            chack_databuff[0] = 0xF3;

            chack_databuff[1] = BLE_INFO.CTL_FLASH.INFO_Array[num].Type_Data.Button.eSubDevice_Color;
            if (chack_databuff[1] < eSUBDEVICE_COLOR_YELLOW)   // 错误
            {
              LOG_ERR("ERROR data");
              break;
            }
            chack_databuff[2] = BLE_INFO.CTL_FLASH.INFO_Array[num].Device_Type;
            chack_databuff[3] = BLE_INFO.CTL_FLASH.INFO_Array[num].Type_Data.Button.SOC;
            chack_databuff[4] = defice_type;
            memcpy(&chack_databuff[5], BLE_INFO.CTL_FLASH.INFO_Array[num].Type_Data.Button.Config, 15);
            
            int ret = BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, chack_databuff, 20);
            LOG_DBG("cRET:%d",ret);
            if(ret == -ENOMEM)//空间不足 放入队列中等待后续发送
            {
              LOG_HEXDUMP_WRN(chack_databuff,20,"MSGQ");
              k_msgq_put(&Queue_PPL_WrIte, &chack_databuff[0], K_NO_WAIT);  // 放入队列内
              start_queue_flag = true;
            }
            else
            {
              LOG_HEXDUMP_DBG(chack_databuff, 20, "chack_databuff:");
            }
          }

          
        }
        break;
        case Reply_ATT_CHACK_ALL_EVENT:  // 0xFB查询全部触发结果的设置 ： 
        {
          // /回复0xF2→前文的2，触发结果的设置 
          for (uint8_t num = 0; num < eFLASH_Game_EVENT_ID_END - eFLASH_Game_EVENT_ID_START; num++)  // 把有效的全部发上去同步
          {
            if (!(GAME_INFO.Game_EVENT.ID_BitFLag & (1 << num)))  // 跳过无效设备
            {
              continue;
            }
            uint8_t ppl_data_buff[20];
            ppl_data_buff[0] = 0xF2;  
            memcpy(&ppl_data_buff[1], GAME_INFO.Game_EVENT.Config[num].All_Arrays, 19);
            int ret = BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, ppl_data_buff, 20);  // 发送给APP
            LOG_DBG("pRET:%d", ret);
            if (ret == -ENOMEM)  // 没发送成功 放入队列中等待后续发送
            {
              LOG_HEXDUMP_WRN(ppl_data_buff, 20, "MSGQ");
              k_msgq_put(&Queue_PPL_WrIte, &ppl_data_buff[0], K_NO_WAIT);  // 放入队列内
              start_queue_flag = true;
            }
            else
            {
              LOG_HEXDUMP_DBG(ppl_data_buff, 20, "ppl_data_buff:");
            }
          }
        }
        break;
        case Reply_ATT_CHACK_START_STOP:  // 0xFD查询启动/停止设置
        {
          //回复0xF4→前文的4，启动/停止设置
          uint8_t ppl_data_buff[14];
          ppl_data_buff[0] = 0xF4;
          memcpy(&ppl_data_buff[1],GAME_INFO.Game_START_STOP_CONF.All_Arrays,13);
          int ret = BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, ppl_data_buff, 14);  // 发送给APP
          LOG_DBG("pRET:%d", ret);
          if (ret == -ENOMEM)  // 没发送成功 放入队列中等待后续发送
          {
            LOG_HEXDUMP_WRN(ppl_data_buff, 14, "MSGQ");
            k_msgq_put(&Queue_PPL_WrIte, &ppl_data_buff[0], K_NO_WAIT);  // 放入队列内
            start_queue_flag = true;
          }
          else
          {
            LOG_HEXDUMP_DBG(ppl_data_buff, 14, "ppl_data_buff:");
          }
        }
        break;
        case Receive_Game_Stop: //0x12 停止配件玩法（强制结束配件玩法） 
        {
          GAME_INFO.Game_State = eInitial;//初始状态
          if(GAME_INFO.Game_Play.Hover_Dog.Mode_Start_Flag == true)//巡回犬模式启动
          {
            GAME_INFO.Game_Play.Hover_Dog.Mode_Start_Flag = false;//停止巡回犬模式
            uint8_t send_buff[2];
            int8_t conn_idex = BLE_ATT_Get_CTL_CONN_Index_Fo_SubDevice(GAME_INFO.Game_Play.Hover_Dog.Triger_ID);  // 取触发索引进行关灯
            if (conn_idex > -1)
            {
              send_buff[0] = 0x70;
              send_buff[1] = 0;
              BLE_CTL_Write(BLE_INFO.CTL_CONN[conn_idex].CONN, send_buff, 2);  // 取消肩灯显示
            }
          }
          BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State,0), 4);

        }
        break;
        case Receive_Game_Start:  // 0x11 APP通过指令启动配件玩法（可以在配件玩法工作中生效，但无意义）
        {
          uint16_t statr_time_A = swap_int16(GAME_INFO.Game_START_STOP_CONF.Data.Statr_Time_A);//起始定时时间1
          uint16_t statr_time_B = swap_int16(GAME_INFO.Game_START_STOP_CONF.Data.Statr_Time_B);//起始定时时间2
          uint16_t max_time     = swap_int16(GAME_INFO.Game_START_STOP_CONF.Data.MAX_Time);//最大定时时间
          GAME_INFO.Game_Play.Hover_Dog.Mode   = GAME_INFO.Game_START_STOP_CONF.Data.Waveform_A;//巡回犬模式
          GAME_INFO.Game_Play.Hover_Dog.Number_Config = GAME_INFO.Game_START_STOP_CONF.Data.Waveform_B;//巡回次数
          (GAME_INFO.Game_START_STOP_CONF.Data.Button & 0x01) ? (GAME_INFO.Game_Play.Hover_Dog.Start_EN = true) : (GAME_INFO.Game_Play.Hover_Dog.Start_EN = false);  // bit1位
          (GAME_INFO.Game_START_STOP_CONF.Data.Button & 0x02) ? (GAME_INFO.Game_Play.Hover_Dog.Off_Line_Mode_AI_OFF = true):(GAME_INFO.Game_Play.Hover_Dog.Off_Line_Mode_AI_OFF = false);//bit2位
          GAME_INFO.Game_Play.Time_Delay_Game_Start = swap_int16(GAME_INFO.Game_START_STOP_CONF.Data.Conf_Time_Delay_Game_Start);//延迟启动游戏
          
          if(statr_time_A !=0 && statr_time_B != 0)
          {
            if(statr_time_A == statr_time_B)//固定
            {
              GAME_INFO.Game_Play.Time_Over = statr_time_A;// 设定开锁时间 记录开锁时间
            }
            else //随机
            {
              uint16_t random = (uint16_t)k_uptime_get_32();//取低位时间变化 用于随机
              uint16_t time_buff=0;
              random ^= random << 3;
              random ^= random >> 5;
              random ^= random << 2;
              (statr_time_A >= statr_time_B)?(time_buff =map(random,0,0xFFFF,statr_time_B,statr_time_A)):(time_buff =map(random,0,0xFFFF,statr_time_A,statr_time_B)); 
              LOG_DBG("!= time_buff:%d",time_buff);
              GAME_INFO.Game_Play.Time_Over  = time_buff;//设定开锁时间 记录开锁时间
            }
            (max_time != 0)?(GAME_INFO.Game_Play.Time_Remaining  = max_time):(GAME_INFO.Game_Play.Time_Remaining = 0xffff);//如果最大定时时间数据为0，代表不做最大定时时间的保护
          }

          //先检测是否延迟启动
          if(GAME_INFO.Game_Play.Time_Delay_Game_Start > 0)//延迟启动游戏
          {
            GAME_INFO.Game_State = eDelay;//延时中
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State,GAME_INFO.Game_Play.Time_Delay_Game_Start), 4);
          }
          else
          {
            GAME_INFO.Game_State = eUndeway;//玩法生效
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_Start_Array(GAME_INFO.Game_State,GAME_INFO.Game_Play.Time_Over), 4);
          }
          GAME_INFO.Game_Play.Hover_Dog.Mode_Start_Flag = true;//开启巡回犬模式
        }
        break;
        case Receive_Game_SetSubDevice:  // 0x30/修改配件触发设置（配件玩法工作中不生效）
        {
          // 收到 共17字节，0x30（1字节）+颜色（1字节）+触发设置（15字节）
          const eSubDevice_Color Color_ID = data_buff[1];
          if (Color_ID > eSUBDEVICE_COLOR_WHITE && Color_ID < eSUBDEVICE_COLOR_YELLOW)  // 如果颜色信息在01-07之外, 返回错误码0x02
          {
            LOG_WRN(" Game_SetSubDevice COLOR DATA ERROR!!");
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_ERROR_Array(Receive_Game_SetSubDevice, eERRNO_ATT_invalidLEN), 3);
            break;
          }
          else if (GAME_INFO.Game_State == eUndeway)  // 如果在配件玩法执行中收到此命令，返回通用错误码0x03
          {
            LOG_WRN(" Game_SetSubDevice Game_State ERROR!!");
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_ERROR_Array(Receive_Game_SetSubDevice, eERRNO_ATT_invalidSTA), 3);
            break;
          }
          else if (BLE_ATT_Find_Flash_ID_EQ(&BLE_INFO.CTL_FLASH, Color_ID) == -1)  // 如果没有绑定对应颜色的子设备, 返回错误码0x04
          {
            LOG_WRN(" Game_SetSubDevice ID ERROR!!");
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_ERROR_Array(Receive_Game_SetSubDevice, eERRNO_ATT_invalidTGT), 3);
            break;
          }
          int8_t conn_idex = BLE_ATT_Get_CTL_CONN_Index_Fo_SubDevice(Color_ID);                // 检测设备在不在线
          int8_t Flash_ID  = BLE_ATT_Find_Flash_ID_EQ(&BLE_INFO.CTL_FLASH, Color_ID);  // 获取FLASH的ID
          LOG_DBG("conn_idex:%d", conn_idex);
          if (conn_idex < 0)  // 不在线
          {
            // 将其保存下来，在连接对应颜色配件之后将设置写入
            if (Flash_ID != -1)
            {
              memcpy(BLE_INFO.CTL_FLASH.INFO_Array[Flash_ID].Type_Data.Button.Config, &data_buff[2], sizeof(BLE_INFO.CTL_FLASH.INFO_Array[Color_ID - 1].Type_Data.Button.Config));
              Flash_Control_write(&DG_LAB_FLASH, eFLASH_BLE_Device_ID_START + Flash_ID, &BLE_INFO.CTL_FLASH.INFO_Array[Flash_ID], sizeof(BLE_INFO.CTL_FLASH.INFO_Array[Flash_ID]));
              Updata_Flash_From_WhiteList();  // 更新一下33
              LOG_HEXDUMP_DBG(BLE_INFO.CTL_FLASH.INFO_Array[Flash_ID].Type_Data.Button.Config, 15, "WRITE FALSH");
            }
          }
          else  // 在线
          {
            // 向子设备0x150A特性写入以下数据：数据包类型id 0x50 颜色（1字节）+触发设置（15字节）
            uint8_t ctl_data_buff[17];
            ctl_data_buff[0] = SET_GAME_CONFIG;  // 0x50
            ctl_data_buff[1] = Color_ID;
            memcpy(&ctl_data_buff[2], &data_buff[2], 15);
            LOG_HEXDUMP_DBG(ctl_data_buff, 17, "Send");
            BLE_CTL_Write(BLE_INFO.CTL_CONN[conn_idex].CONN, ctl_data_buff, 17);  // 透传给子设备
            // 更新FLASH数据
            memcpy(BLE_INFO.CTL_FLASH.INFO_Array[Flash_ID].Type_Data.Button.Config, &data_buff[2], sizeof(BLE_INFO.CTL_FLASH.INFO_Array[Color_ID - 1].Type_Data.Button.Config));
            Flash_Control_write(&DG_LAB_FLASH, eFLASH_BLE_Device_ID_START + Flash_ID, &BLE_INFO.CTL_FLASH.INFO_Array[Flash_ID], sizeof(BLE_INFO.CTL_FLASH.INFO_Array[Flash_ID]));
            Updata_Flash_From_WhiteList();  // 更新一下
            LOG_HEXDUMP_DBG(BLE_INFO.CTL_FLASH.INFO_Array[Flash_ID].Type_Data.Button.Config, 15, "save falsh");
          }
          // 向APP回复 共20字节，0x31（1字节）+颜色（1字节）+ 子设备类型（1字节）+电量百分比（1字节）+连接状态（1字节）+触发设置（15字节）
          uint8_t ppl_data_buff[20];
          ppl_data_buff[0] = Reply_Game_SetSubDevice;//0x31
          ppl_data_buff[1] = Color_ID;
          ppl_data_buff[2] = BLE_INFO.CTL_FLASH.INFO_Array[Flash_ID].Device_Type;
          ppl_data_buff[3] = BLE_INFO.CTL_FLASH.INFO_Array[Flash_ID].Type_Data.Button.SOC;
          (conn_idex < 0) ? (ppl_data_buff[4] = 0) : (ppl_data_buff[4] = 1);
          memcpy(&ppl_data_buff[5], &data_buff[2], 15);
          BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, ppl_data_buff, 20);  // 发送给APP
        } 
        break;
        case Receive_Game_Event:// 0x20增加/修改指定触发ID的触发结果（配件玩法工作中不生效）
        {
          uint8_t _event_id = data_buff[1];  // 触发ID
          if(_event_id>0) _event_id-- ;
          if (GAME_INFO.Game_State == eUndeway)  // 如果在配件玩法执行中收到此命令，返回通用错误码0x03
          {
            LOG_WRN(" Game_Event Game_State ERROR!!");
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_ERROR_Array(Receive_Game_Event, eERRNO_ATT_invalidSTA), 3);
            break;
          }
          else if(_event_id > GAME_EVENT_ID_MAX )//从1开始计算 ID出错
          {
            LOG_WRN(" Game_Event ID ERROR!!");
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_ERROR_Array(Receive_Game_Event, eERRNO_ATT_invalidLEN), 3);
            break;
          }
          else//ID正确
          {
            // if (GAME_INFO.Game_EVENT.Config[_event_id].Data.ID == 0)  // 新增ID
            memcpy(GAME_INFO.Game_EVENT.Config[_event_id].All_Arrays, &data_buff[1], 19);//数据放进去
            #if 0
            LOG_DBG("Game_EVENT INFO:");
            LOG_INF("%02X %01X %01X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %04X %04X %02X", 
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.ID,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.EventTime_Again,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.Skip_Delay,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.COM_A_EN,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.COM_B_EN,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.Temp_Strength_A_Way,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.Temp_Strength_A_Last,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.Temp_Strength_A,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.Temp_Strength_B_Way,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.Temp_Strength_B_Last,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.Temp_Strength_B,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.Strength_A_Last,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.Strength_A,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.Strength_B_Last,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.Strength_B,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.GameTime_Last,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.GameTime,
                    GAME_INFO.Game_EVENT.Config[_event_id].Data.EventTime);
            k_sleep(K_MSEC(100)); // 延迟100ms
            LOG_HEXDUMP_INF(GAME_INFO.Game_EVENT.Config[_event_id].All_Arrays,19,"data:");
            k_sleep(K_MSEC(100)); // 延迟100ms
            #endif
            // 向APP回复 共20字节，0x21（1字节）+触发事件ID（1字节）+触发生效期间是否允许重新触发（半字节） +停止/跳过延时启动游戏（半字节）+输出指定波形（2字节）+
            // 强度临时改变（6字节）+强度永久改变（4字节）+游戏时间改变（4字节）+触发生效时间（1字节）
            uint8_t ppl_data_buff[20];
            ppl_data_buff[0] = Reply_Game_Event;  // 0x21
            memcpy(&ppl_data_buff[1], &GAME_INFO.Game_EVENT.Config[_event_id].All_Arrays[0], 19);
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, ppl_data_buff, 20);  // 发送给APP
            
            // 计算剩余空间 调试用
            rc = Flash_Control_free_space(&DG_LAB_FLASH);
            if (rc < 0)
            {
              LOG_ERR("Error while calculating free space, rc=%d\n", rc);
              break;
            }
            LOG_WRN("DG_LAB_FLASH: %d bytes",rc);
            //把数据存入FLASH
            Flash_Control_write(&DG_LAB_FLASH, eFLASH_Game_EVENT_ID_START + (_event_id), &GAME_INFO.Game_EVENT.Config[_event_id].All_Arrays[0],
                                sizeof(GAME_INFO.Game_EVENT.Config[_event_id].All_Arrays));
          }
        }
        break;
        case Receive_Game_DeleteID: //0x28 删除指定ID触发结果（配件玩法工作中不生效）
        {
          uint8_t _event_id = data_buff[1];  // 触发ID
          if(_event_id>0) _event_id-- ;
          if(_event_id > GAME_EVENT_ID_MAX)//从1开始计算 ID出错 返回通用错误码0x02
          {
            LOG_WRN(" Game_DeleteID ID ERROR!!");
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_ERROR_Array(Receive_Game_Event, eERRNO_ATT_invalidLEN), 3);
            break;
          }
          else if (GAME_INFO.Game_State == eUndeway)  // 如果在配件玩法执行中收到此命令，返回通用错误码0x03
          {
            LOG_WRN(" Game_DeleteID Game_State ERROR!!");
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_ERROR_Array(Receive_Game_DeleteID, eERRNO_ATT_invalidSTA), 3);
            break;
          }
          else if (GAME_INFO.Game_EVENT.Config[_event_id].Data.ID ==0)  // 如果要删除的触发事件不存在，返回通用错误码0x04
          {
            LOG_WRN(" Game_DeleteID Game_EVENT ERROR!!");
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_ERROR_Array(Receive_Game_DeleteID, eERRNO_ATT_invalidTGT), 3);
            break;
          }
          memset(&GAME_INFO.Game_EVENT.Config[_event_id].All_Arrays[0], 0,19);//清空
          rc = Flash_Control_delete(&DG_LAB_FLASH, eFLASH_Game_EVENT_ID_START+(_event_id));
          if (rc) 
          {
            LOG_ERR("Error ID DG_LAB_FLASH , rc=%d\n", eFLASH_Game_EVENT_ID_START+(_event_id));
            return;
          }
          BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_Game_DeleteID_Array(_event_id), 2);  // 发送给APP
        }
        break;
        case Receive_Game_Config:// 0x40 启动/停止设置（配件玩法工作中不生效）
        {
          if (GAME_INFO.Game_State == eUndeway)  // 如果在配件玩法执行中收到此命令，返回通用错误码0x03
          {
            LOG_WRN(" Receive_Game_Config ERROR!!");
            BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, Reply_ERROR_Array(Receive_Game_Config, eERRNO_ATT_invalidSTA), 3);
            break;
          }
          memcpy(GAME_INFO.Game_START_STOP_CONF.All_Arrays, &data_buff[1], 13);//数据放进去
          #if 0
          LOG_DBG("Game_START_STOP_CONF INFO:");
          LOG_INF("0x%04X 0x%02X 0x%02X 0x%02X 0x%02X 0x%04X 0x%04X 0x%04X 0x%02X",
                  swap_int16(GAME_INFO.Game_START_STOP_CONF.Data.Conf_Time_Delay_Game_Start),
                  GAME_INFO.Game_START_STOP_CONF.Data.Strength_A,
                  GAME_INFO.Game_START_STOP_CONF.Data.Strength_B,
                  GAME_INFO.Game_START_STOP_CONF.Data.Waveform_A,
                  GAME_INFO.Game_START_STOP_CONF.Data.Waveform_B,
                  swap_int16(GAME_INFO.Game_START_STOP_CONF.Data.Statr_Time_A),
                  swap_int16(GAME_INFO.Game_START_STOP_CONF.Data.Statr_Time_B),
                  swap_int16(GAME_INFO.Game_START_STOP_CONF.Data.MAX_Time),
                  GAME_INFO.Game_START_STOP_CONF.Data.Button);
          #endif
          // 计算剩余空间 调试用
          rc = Flash_Control_free_space(&DG_LAB_FLASH);
          if (rc < 0)
          {
            LOG_ERR("Error while calculating free space, rc=%d\n", rc);
            break;
          }
          LOG_WRN("DG_LAB_FLASH: %d bytes",rc);
          //把数据存入FLASH
          Flash_Control_write(&DG_LAB_FLASH, eFLASH_Game_START_STOP_CONFIG, &GAME_INFO.Game_START_STOP_CONF.All_Arrays[0],
                              sizeof(GAME_INFO.Game_START_STOP_CONF.All_Arrays));
          
          // uint16_t statr_time_A = swap_int16(GAME_INFO.Game_START_STOP_CONF.Data.Statr_Time_A);//起始定时时间1
          // uint16_t statr_time_B = swap_int16(GAME_INFO.Game_START_STOP_CONF.Data.Statr_Time_B);//起始定时时间2
          // uint16_t max_time     = swap_int16(GAME_INFO.Game_START_STOP_CONF.Data.MAX_Time);//最大定时时间
          // GAME_INFO.Game_Play.Hover_Dog.Mode   = GAME_INFO.Game_START_STOP_CONF.Data.Waveform_A;//巡回犬模式
          // GAME_INFO.Game_Play.Hover_Dog.Number_Config = GAME_INFO.Game_START_STOP_CONF.Data.Waveform_B;//巡回次数
          // (GAME_INFO.Game_START_STOP_CONF.Data.Button & 0x01) ? (GAME_INFO.Game_Play.Hover_Dog.Start_EN = true) : (GAME_INFO.Game_Play.Hover_Dog.Start_EN = false);  // bit1位
          // (GAME_INFO.Game_START_STOP_CONF.Data.Button & 0x02) ? (GAME_INFO.Game_Play.Hover_Dog.Off_Line_Mode_AI_OFF = true):(GAME_INFO.Game_Play.Hover_Dog.Off_Line_Mode_AI_OFF = false);//bit2位

          // GAME_INFO.Game_START_STOP_CONF.Data.Conf_Time_Delay_Game_Start = GAME_INFO.Game_Play.Time_Delay_Game_Start = swap_int16(GAME_INFO.Game_START_STOP_CONF.Data.Conf_Time_Delay_Game_Start);//延迟启动游戏
          // if(statr_time_A !=0 && statr_time_B != 0)
          // {
          //   if(statr_time_A == statr_time_B)//固定
          //   {
          //     GAME_INFO.Game_Play.Time_Over = GAME_INFO.Game_Play.Conf_Time_Over = statr_time_A;// 设定开锁时间 记录开锁时间
          //     LOG_DBG("== time_buff:%d",GAME_INFO.Game_Play.Conf_Time_Over);
          //   }
          //   else //随机
          //   {
          //     uint16_t random = (uint16_t)k_uptime_get_32();//取低位时间变化 用于随机
          //     uint16_t time_buff=0;
          //     random ^= random << 3;
          //     random ^= random >> 5;
          //     random ^= random << 2;
          //     (statr_time_A >= statr_time_B)?(time_buff =map(random,0,0xFFFF,statr_time_B,statr_time_A)):(time_buff =map(random,0,0xFFFF,statr_time_A,statr_time_B)); 
          //     LOG_DBG("!= time_buff:%d",time_buff);
          //     GAME_INFO.Game_Play.Time_Over = GAME_INFO.Game_Play.Conf_Time_Over = time_buff;//设定开锁时间 记录开锁时间
          //   }
          //   (max_time != 0)?(GAME_INFO.Game_Play.Time_Remaining = GAME_INFO.Game_Play.Conf_Time_Remaining = max_time):(GAME_INFO.Game_Play.Time_Remaining = GAME_INFO.Game_Play.Conf_Time_Remaining = 0xffff);//如果最大定时时间数据为0，代表不做最大定时时间的保护
          // }
          uint8_t send_buff[14];
          send_buff[0] = Reply_Game_Config;//0x40
          memcpy(&send_buff[1],&data_buff[1],13);
          BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX, send_buff, 14);  // 发送给APP
        } 
        break;
        default:
        break;
      }
    }
    break;
    case ePPL_CB_TYPE_Write_OTA_DATA:
    {
      const uint8_t *data = "OTA DATA";
      BLE_PPL_Write(ePPL_CB_TYPE_Notify_INFO_HENV, data,8);  // 测试发送函数
    }
    break;
    case ePPL_CB_TYPE_Write_OTA_SYNC:
    {
      const uint8_t *data = "OTA SYNC";
      BLE_PPL_Write(ePPL_CB_TYPE_Notify_OTA_SYNC, data,8);  // 测试发送函数
    }
    break;
    default:
      break;
  }
  if(start_queue_flag == true)
  {
    k_work_schedule(&ppl_write_work, K_MSEC(PPL_WTRIE_TIMEOUT_MS));  // 启动自动重连工作队列
  }
}

/// @brief  读取ATT数据的回调函数
static ssize_t PPL_Read_CB(ePPL_CB_Type_t _type, struct bt_conn *conn, const struct bt_gatt_attr *attr, void *buf, uint16_t* buf_len, uint16_t* offset)
{
  switch (_type)
  {
    // case ePPL_CB_TYPE_Read_OMS_TX:
    // {
    //   const char *data = "OMS_TX";
    //   return bt_gatt_attr_read(conn, attr, buf, *buf_len, *offset, data, strlen(data));
    // }
    case ePPL_CB_TYPE_Read_OMS_TX_AUTHEN:
    {
      const char *data = "OMS_TX_AUTHEN";
      return bt_gatt_attr_read(conn, attr, buf, *buf_len, *offset, data, strlen(data));
    }
    case ePPL_CB_TYPE_Read_INFO_HWCD:
    {
      // const char *data = "INFO_HWCD";
      LOG_DBG("Read_INFO_HWCD");
      return bt_gatt_attr_read(conn, attr, buf, *buf_len, *offset, DGLAB_ID_DIS_BUILD, 2);
    }
    case ePPL_CB_TYPE_Read_INFO_HENV:
    {
      const char data[] = {0x47};
       LOG_DBG("ePPL_CB_TYPE_Read_INFO_HENV");
      return bt_gatt_attr_read(conn, attr, buf, *buf_len, *offset, data, 1);
    }
    default:
      return BT_GATT_ERR(BT_ATT_ERR_ATTRIBUTE_NOT_FOUND);
  }
}




/// @brief 写入数据的合法性检测
static void Validity_Check_handler(struct k_work *work) 
{
  int8_t OLd_Device_Flag = -1; // 旧设备标志，默认为-1 否则记录存储falsh的位置
  bt_addr_le_t mac_buff;
  // bt_addr_le_t *pmac_buff;
  uint8_t index_valodiity = 0;
  Updata_Flash_From_WhiteList();//更新一下白名单
  // if(array_position != 0)//有数据需要验证
  if(BLE_MACList_Is_Empty(&BLE_List_Validity) != true)//队列不为空
  {
    // array_position -=1;//记录是1开始 所以减1 回到0
    // mac_buff = BLE_INFO.CTL_CONN[array_position].mac; //获取MAC
    // BLE_MACList_Get_First(&BLE_List_Validity,&mac_buff);//获取第一个MAC
    // BLE_MACList_Get_CTL_Index(&BLE_List_Validity,&mac_buff,&index_valodiity);//获取索引
    // BLE_MACList_Check_Location(&BLE_List_Validity,&mac_buff);

    for(;index_valodiity<CONFIG_BT_MAX_CONN;index_valodiity++)
    {
      BLE_MACList_Get_At(&BLE_List_Validity,index_valodiity,&mac_buff);//查MAC
      if(bt_addr_le_equal(&BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].MAC,&mac_buff) != true)//这个MAC没被记录
      {
        // BLE_MACList_Get_CTL_Index(&BLE_List_Validity,&mac_buff,&BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].conn_index);
        BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].MAC = mac_buff;
        break;
      }
    }
    LOG_DBG("index_valodiity:%d",index_valodiity);
    LOG_MAC(&mac_buff);
    //新旧设备查询
    for(uint8_t num=0;num<eFLASH_BLE_Device_ID_END-eFLASH_BLE_Device_ID_START;num++)//只查已经找到的数据进行对比
    {
      if (!(BLE_INFO.CTL_FLASH.Valid_Device_BitFLag & (1 << num))) //跳过无效设备
      {
        continue; 
      }
      if(bt_addr_le_equal(&mac_buff, &BLE_INFO.CTL_FLASH.INFO_Array[num].MAC) == true)//比较两个MAC地址是否相同 相同说明是个旧设备
      {
        OLd_Device_Flag = num; // 记录存储falsh的位置
        LOG_DBG("Old Device:%d",OLd_Device_Flag);
      }
      if(OLd_Device_Flag != -1)//找到了
      {
        break;//退出 不用继续找了
      }
    } 
    //通过MAC推导索引 用于发送数据
    // int8_t ble_ctl_index=BLE_INFO.CTL_CONF.conn_count;//从当前连接数开始找
    // for(;ble_ctl_index>=0;ble_ctl_index--)//防止异步导致跳过合法性验证
    // {
    //   if(memcmp(&mac_buff,&BLE_INFO.CTL_CONN[ble_ctl_index].mac,sizeof(bt_addr_le_t)) == 0 )//找到了
    //   {
    //     break;//退出 不用继续找了
    //   }
    // }
    
    BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].SendNum_Count = 0;  // 重置发送次数计数器
    memset(BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Data, 0, sizeof(BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Data));  // 清空数据缓存区
    if (OLd_Device_Flag == -1)  // 是新设备
    {
      BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Device_NEW_Flag = true;             // 标记为新设备
      BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Data[0]         = SET_GAME_CONFIG;  // 0x50
      eSubDevice_Color First_Invalid_index = 0xFF;  // 寻找第一个无效的位置索引 用于灯分配
      for (uint8_t i = 0; i < (eFLASH_BLE_Device_ID_END - eFLASH_BLE_Device_ID_START); i++)
      {
        if (!(BLE_INFO.CTL_FLASH.Valid_Device_BitFLag & (1 << i)))
        {
          First_Invalid_index = (eSubDevice_Color)i;
          // data_buff[1]=First_Invalid_index+3;//分配一个灯的索引 给新设备
          BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Data[1] = First_Invalid_index + 1;  // 分配一个灯的索引 给新设备
          BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].DelaySendTime_x100ms = 5; //延迟500MS
          break;
        }
      }
      LOG_DBG("New Device:%d", First_Invalid_index);
    }
    else  // 是旧设备
    {
      BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Device_NEW_Flag = false;            // 标记为旧设备
      BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Data[0]         = SET_GAME_CONFIG;  // 0x50
      
      memcpy(&BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Data[1], &BLE_INFO.CTL_FLASH.INFO_Array[OLd_Device_Flag].Type_Data.Button,
             sizeof(BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Data)-1);  // 复制旧设备数据
      BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].DelaySendTime_x100ms = 5; //延迟500MS
      LOG_HEXDUMP_DBG(&BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Data, 32, "READ data:");
    }
    
  }
}

/// @brief  PPL队列发送
static void ppl_write_handler(struct k_work *work)
{
  uint8_t queue_data[20]={0};
  if(k_msgq_peek(&Queue_PPL_WrIte, queue_data)  == -ENOMSG) //没数据了
  {
    return;
  }
  // if(k_msgq_get(&Queue_PPL_WrIte,queue_data, K_NO_WAIT)  == -ENOMSG) //没数据了
  // {
  //   return;
  // }
  int ret = BLE_PPL_Write(ePPL_CB_TYPE_Notify_OMS_TX,queue_data, 20);  // 发送给APP
  LOG_DBG("qRET:%d",ret);
  if(ret == 0)//发送成功
  {
    k_msgq_get(&Queue_PPL_WrIte,queue_data, K_NO_WAIT);
    LOG_HEXDUMP_DBG(queue_data, 20, "handler:");
  }
  k_work_schedule(&ppl_write_work, K_MSEC(PPL_WTRIE_TIMEOUT_MS));  // 启动自动重连工作队列
}

/// @brief  定时执行任务
static void BLE_ATT_LOOP(struct k_work *work)
{
  struct bt_conn_le_create_param _create_param = {
    .options = BT_CONN_LE_OPT_NONE,        // 连接选项:无
    .interval = INIT_INTERVAL,              // 初始化扫描间隔
    .window = INIT_WINDOW,                  // 初始化扫描窗口
    .interval_coded = 0,                    // 编码PHY扫描间隔:不使用
    .window_coded = 0,                      // 编码PHY扫描窗口:不使用
    .timeout = 11,                          // 连接超时时间
};
struct bt_le_conn_param _conn_param = {
    .interval_min = CONN_INTERVAL,    // 最小连接间隔
    .interval_max = CONN_INTERVAL,    // 最大连接间隔
    .latency = CONN_LATENCY,         // 从机延迟响应的连接事件数量
    .timeout = 50,                  // 连接超时时间,单位10ms
};
  static uint8_t time_count_200mS=0;
  time_count_200mS++; 
  /*一秒一次*/
  if (time_count_200mS >= (ATT_LOOP_TIME_MS / 50))//
  {
    time_count_200mS = 0;
    /*------------------自动重连工作队列------------------------*/
    {
      // static bt_addr_le_t conn_mac_last;
      if (BLE_INFO.CTL_CONF.Auto_SCAN_Flag ==  true)  //开启扫描
      {
        BLE_INFO.CTL_CONF.Auto_SCAN_Flag  = false;
        LOG_DBG(" Update_Offline");
        if(BLE_INFO.CTL_CONF.conn_count < BLE_INFO.CTL_FLASH.Valid_Count)//有设备离线  链接数< flash的数 
        {
          Update_Offline_Device_Queue();// 更新离线队列名单
        }
      }
      if (BLE_INFO.CTL_CONF.Scan_Mode == eNone_SCAN && BLE_INFO.CTL_CONF.OFFLINE.Await_Updata_Flag == false)  // 无扫描状态才可以启动
      {
        uint8_t Offline_count = BLE_MACList_Get_Count(&BLE_List_Offline);
        // if (BLE_INFO.CTL_CONF.OFFLINE.Count_Num_Max > 0 &&  BLE_INFO.CTL_CONF.CONN == NULL)  // 有数据
        if(Offline_count > 0 &&  BLE_INFO.CTL_CONF.CONN == NULL)// 有数据
        {
          static uint8_t sned_conut=0;//发送计数
          bt_addr_le_t mac_buff;
          if(sned_conut > Offline_count-1)
          {
            sned_conut=0;
          }
          BLE_MACList_Get_At(&BLE_List_Offline,sned_conut,&mac_buff);//获取MAC
          sned_conut++;
          // LOG_DBG("sned_conut:%d",sned_conut);
          bt_conn_le_create(&mac_buff, &_create_param, &_conn_param, &BLE_INFO.CTL_CONF.CONN);
          // LOG_MAC(&mac_buff);
        }
      }
    }
  }
  //------------------------100mS------------------------------------------------------------
  { /*------------------合法性重发工作队列------------------------*/
    static uint8_t num_count = 0; // 发送循环计数 100ms++ 用于不同设备的发送
    uint8_t validity_count = BLE_MACList_Get_Count(&BLE_List_Validity);
    if (validity_count > 0)// 队列不为空有数据验证  
    {
      uint8_t index_valodiity = 0;
      bt_addr_le_t mac_buff;
      if(num_count < validity_count)//有效数组才发
      {
        BLE_MACList_Get_At(&BLE_List_Validity, num_count, &mac_buff);                // 依次取出MAC地址
        // BLE_MACList_Get_CTL_Index(&BLE_List_Validity, &mac_buff, &index_valodiity);  // 通过MAC获取索引
        index_valodiity = BLE_MACList_Check_Location(&BLE_List_Validity,&mac_buff);
        int8_t conn_idex = Get_CTL_CONN_Index(&mac_buff);//查索引位置
        if(BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].DelaySendTime_x100ms > 0)//延迟发送
        {
          BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].DelaySendTime_x100ms--; 
        }
        else
        {
          BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].SendNum_Count++;
          if (BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].SendNum_Count >= 5)  // 超出了重发次数 把设备MAC写入黑名单中 并且断开它
          {
            LOG_INF("Disconn && Black List Add");
            BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].SendNum_Count=0;
            BLE_MACList_Add(&BLE_List_BlackList, &mac_buff);  // 加入黑名单
            
            if(conn_idex != -1)
            {
              bt_conn_disconnect(BLE_INFO.CTL_CONN[conn_idex].CONN, BT_HCI_ERR_REMOTE_USER_TERM_CONN);
            }
            if(BLE_MACList_Check(&BLE_List_Offline,&mac_buff) != true)//离线的设备接进来 是不需要扫描的
            {
              BLE_INFO.CTL_CONF.Disconn_Scan_EN_Flag = true;          // 开启断开设备后的扫描!!!!!
            }
            BLE_MACList_Remove(&BLE_List_Validity, &mac_buff);  // 删除MAC
          }
          else
          {
            BLE_CTL_Write(BLE_INFO.CTL_CONN[conn_idex].CONN, BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Data, sizeof(BLE_INFO.CTL_CONF.Validity_Chack[index_valodiity].Data));

          }
        }
      }
      (num_count >= BLE_CTL_Device_Connect_MAX-1)?(num_count=0):(num_count++);
    }
  }

  k_work_schedule(&att_loop_work, K_MSEC(ATT_LOOP_TIME_MS)); 
}


/**
 * @brief 初始化蓝牙ATT通信数据处理函数
 *
 * @return NONE
 */
void BLE_ATT_COM_FUNC_Init(void)
{
  BLE_MACList_Init(&BLE_List_BlackList, BLE_ATT_COM_List_BlackList_MAX);  // 初始化黑名单列表
  BLE_MACList_Init(&BLE_List_Validity, BLE_ATT_COM_List_BlackList_MAX);   // 初始化合法性验证列表
  BLE_MACList_Init(&BLE_List_Offline, BLE_ATT_COM_List_BlackList_MAX);    // 初始化离线设备列表
  
  BLE_PPL_GATT_FUNC_CB_Init(PPL_Receive_CB, PPL_Read_CB);  // 初始化外围设备GATT函数回调
  BLE_CTL_GATT_FUNC_CB_Init(CTL_Receive_CB);               // 初始化中央设备接收信息回调
  Update_Offline_Device_Queue();//更新离线队列名单
  Queue_PPL_WrIte_Init();//初始化消息队列
  Updata_Flash_From_GameEvent();//更新FLASH下的玩法 开机时初始化一遍就行了
  Flash_Control_read(&DG_LAB_FLASH, eFLASH_Game_START_STOP_CONFIG,GAME_INFO.Game_START_STOP_CONF.All_Arrays, sizeof(GAME_INFO.Game_START_STOP_CONF.All_Arrays));//获取启动停止设置值
  
  k_work_init_delayable(&scan_timeout_work, scan_timeout_handler);  // 初始化带延迟的扫描超时工作队列
  k_work_init_delayable(&ppl_write_work, ppl_write_handler);      // 初始化PPl数据发送工作队列
  k_work_init_delayable(&att_loop_work, BLE_ATT_LOOP);              // 初始化自动重连工作队列
  
  
  k_work_init(&BLE_INFO.CTL_FUNC.validity_check_work, Validity_Check_handler);  // 初始化写合法性工作队列
  
  // Offline_Device_Queue_Init();//初始化自动重连工作队列
  
  k_work_schedule(&att_loop_work, K_MSEC(ATT_LOOP_TIME_MS));  // 启动自动重连工作队列
}

