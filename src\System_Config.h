#ifndef SYSTEM_CONFIG_H_
#define SYSTEM_CONFIG_H_
#include "BLE_MACList.h"
#include "central_and_peripheral.h"
#include "Game_Common.h"
#include "stdbool.h"
//#include "System_Config.h"

#define BLE_ATT_COM_List_BlackList_MAX 6                              // 黑名单最大容量 动态申请 要限制
// extern ST_Ble_MACList BLE_List_BlackList;   // 黑名单列表
// extern ST_Ble_MACList BLE_List_WhiteList;   // 白名单列表 用于记录保存在flash内的信息
enum
{
  eFLASH_BLE_CONN_INFO_ID = 0x01,        //蓝牙连接信息存储
  eFLASH_BLE_Device_ID_START=0x02,       //中央模式下连接设备开始
  eFLASH_BLE_Device_ID_END=eFLASH_BLE_Device_ID_START+BLE_CTL_Device_Connect_MAX,      //中央模式下连接设备结束
  eFLASH_Game_EVENT_ID_START,             //配件玩法存储
  eFLASH_Game_EVENT_ID_END=eFLASH_Game_EVENT_ID_START+GAME_EVENT_ID_MAX,//配件玩法存储结束
  eFLASH_Game_START_STOP_CONFIG,        //游戏启动停止设置
};

//-----------------------------------硬件配置--------------------------------------------------------
#define Config_Hardware_SubDevice_IRQ_GPIO_Trigger (uint8_t)0  // 子设备IRQ引脚触发方式 1：高电平 0:低电平
#define Config_Hardware_SubDevice_IRQ_Send_Time_mS 1        // 子设备IRQ发送延时时间

//----------------------------------这里是移植气压计的commonTopLevel.h文件-----------------------------


// #define APP_NUM_LIMIT_CORO_MAX 8   // 最大逻辑任务并行数量
// #define APP_NUM_LIMIT_QUEUE_MAX 8  // 最大路由事件队列大小
// #define APP_NUM_LIMIT_MSG_MAX CONFIG_BT_NIMBLE_ATT_PREFERRED_MTU  // IPC传递消息的最大长度

// typedef unsigned char byte;  // 用于表示字节流

// 顶层的逻辑任务(基于定时器)
// typedef struct {
//     esp_timer_create_args_t config;
//     esp_timer_handle_t timerESP;
//     uint64_t timerPeriod_us;
// } appCoro_t;

// 所有顶层运行模式
typedef enum
{
  eIdx_Status_appRun_idle,           // 尚未启动
  eIdx_Status_appRun_stopped,        // 表示顶层逻辑已经停止(用于超长按还原出厂设置等)
  eIdx_Status_appRun_shuttingdown,   // 正在关机中(计划或等待按键释放)
  eIdx_Status_appRun_inOTA,          // OTA专用模式
  eIdx_Status_appRun_chargingNoUsr,  // 无操作的充电状态(关机状态下充电但无按键操作)
  eIdx_Status_appRun_normal,         // 正常工作模式
  eIdx_Status_appRun_inSync,         // 用户数据同步(阻塞式)
} eAppRunStatus_t;

// // 所有游戏玩法模式
// typedef enum
// {
//   eIdx_GAME_MODE_STOP = 0x00,     // 待机状态
//   eIdx_GAME_MODE_STRESS = 0x01,   // 简单气压(菊部收缩)触发
//   eIdx_GAME_MODE_AI = 0x02,       // 使用内置AI高潮强度检测
//   eIdx_GAME_MODE_PROXY = 0xD0,    // 直接上传传感器数据, 嵌入式端不做处理  (简称"直传模式")
//   eIdx_GAME_MODE_FACTORY = 0xFF,  // (特殊应用)工厂模式, 出厂标定、测试等. 此模式必须是上位机直连
// } eAppGameMode_t;

typedef struct _IO_Status_CB
{
  uint32_t time_lastPress;
  uint32_t time_lastRelease;
  size_t cnt_keyDonw_main;  // 近期按键按下的次数, 用于识别按键长短按  // NOTE: 本项目硬件电路仅有唯一的一个按键负责做交互
//   bool flag_keyDonw_main;   // 按键当前是否被按下
} eventIO_t;

// 所有屏幕界面
typedef enum _UI_Screen_Idx
{
  eIdx_UI_Screen_none,             // 无效或未初始化
  eIdx_UI_Screen_homne,            // 主界面
  eIdx_UI_Screen_OTA,              // 进行OTA的界面
  eIdx_UI_Screen_repair,           // 表示当前资源文件不正常, 需要修复
  eIdx_UI_Screen_lowpow,           // 低电量(即将关机)
  eIdx_UI_Screen_charger,          // 充电中
  eIdx_UI_Screen_restore_prepare,  // 准备恢复屏幕
  eIdx_UI_Screen_restore,          // 重置出厂完成

} eUI_idxScreen_t;

// 顶层共用且用户关注的信息字段
typedef struct Data_TL_common
{
//   uint8_t id_subDviceColor;  // 子设备id(眼灯)
//   uint8_t appColor[3];       // 子设备外观编号(肩灯, 主色/辅色/闪烁方式)
  // uint8_t Battery_SOC;    // 电量百分比
//   union
//   {
//     struct
//     {
//       uint8_t lcd_bright_normal;       // 1~100(70);            // 屏幕主亮度设置
//       uint8_t lcd_standbySec;          // 10~250(180), 255;     // 屏幕无操作背光省电延时时长秒数设置
//       uint8_t sys_sleepMin;            // 3~250(30), 255;       // 无操作自动关机延时时长分钟数设置
//       uint8_t lcd_bright_standby;      // 1~100(10);   // 省电屏幕背光亮度设置
//       uint8_t game_sensitivity;        // 1~15(6);     // 触发灵敏度等级设置
//       uint8_t sensor_autoClearPOST;    // 1~2(1);      // 是否开机自动校准清零
//       uint8_t BLE_autoAdvPOST;         // 1~2(1);      // 是否开机自动进入蓝牙连接模式
//       uint8_t game_useFlashingActive;  // 1~2(1);      // 是否使用触发时屏幕爆闪功能
//       uint8_t sensor_filterStep;       // 1~4(1);      // 跳变忽略等级
//       uint8_t ui_rotateIdx;            // 1, 3 // 屏幕旋转方向_1  1: 0度(默认不旋转)、3: 180度(沿长边翻转)
//       // dataI16_t sensor_Clear2;         // -1200~-1, 1~1200(1);   // 读数清零模式
//     };
//     uint8_t ArrayALL[10];  // 用于批量读写配置
//   } APP_LCD_CFG;
//   //   status_machine_ble_t* bleStatus;
//   eAppRunStatus_t status_appRun_new_flag;  // 新切应用层运行标志
//   eAppRunStatus_t status_appRun_last;      // 应用层运行的上一个状态值
//   eAppRunStatus_t status_appRun;           //当前程序运行的标志
// //   eAppGameMode_t status_gameMode;
//   eUI_idxScreen_t usingScreen_new_flag;  // 新切屏幕显示标志
//   eUI_idxScreen_t usingScreen_last;      // 屏幕显示上一个状态值
//   eUI_idxScreen_t usingScreen;
//   eventIO_t* eventIO;
//   //   hw_env_t* hw_env;
//   //   appCoro_t coros[APP_NUM_LIMIT_CORO_MAX];
//   //   RingBuffer_t* bufferOTA;
//   //   wl_handle_t handle_usrFS;
//   uint32_t time_lastInput;  // 记录用户最近一次任意操作的时刻, 用于超时息屏/关机等
} appTL_t;

extern appTL_t GLOBAL_APP_DATA;

#define TIMEINTVL_MAINLOOP                     25  // 主循环(低实时性)的循环间隔(毫秒)

//------------------------------------------默认参数值-------------------------------------------------------------------------------
#define CFG_USR_DEFAULT_lcd_bright_normal      70   // 屏幕主亮度设置(100档位, 越高越亮, 1~100, 1档指能看清的最小亮度, 不允许关背光)
#define CFG_USR_DEFAULT_lcd_standbySec         180  // 屏幕无操作背光省电延时时长秒数设置(10~250, 特殊255表示不省电)
#define CFG_USR_DEFAULT_sys_sleepMin           5    // 无操作自动关机延时时长分钟数设置(1~250, 特殊255表示永不自动关机)
#define CFG_USR_DEFAULT_lcd_bright_standby     10   // 省电屏幕背光亮度设置(1~100 如果设置超过屏幕亮度则会被箝位到屏幕亮度)
#define CFG_USR_DEFAULT_game_sensitivity       6    // 触发灵敏度等级设置(16档位, 越高越容易触发, 1~15)_1字节
#define CFG_USR_DEFAULT_sensor_autoClearPOST   1    // 是否开机自动校准清零(1:禁用、2:启用)
#define CFG_USR_DEFAULT_BLE_autoAdvPOST        1    // 是否开机自动进入蓝牙连接模式(1:禁用、2:启用)
#define CFG_USR_DEFAULT_game_useFlashingActive 1    // 是否使用触发时屏幕爆闪功能(1:禁用、2:启用)_1字节
#define CFG_USR_DEFAULT_sensor_filterStep      1    // 跳变忽略等级(4档位, 越高瞬态抑制越强, 1~3)_1字节
#define CFG_USR_DEFAULT_sensor_Clear2          1    // 读数清零模式(1: 直接显示原始读数、2: 以当前读数为偏置立即清零、大于2: 直接指定偏置)
#define CFG_USR_DEFAULT_ui_rotateIdx           1    // 屏幕旋转方向(1: 0度(默认不旋转)、3: 180度(沿长边翻转))_1字节
#define CFG_USR_DEFAULT(name)                  CFG_USR_DEFAULT_##name

// 所有OTA状态
typedef enum _OTA_Status_Idx
{
  eIdx_Status_OTA_none,        // 无效或未初始化
  eIdx_Status_OTA_inProgress,  // 正在OTA进行中
  eIdx_Status_OTA_Failed,      // 失败
  eIdx_Status_OTA_Done,        // 刚完成一次OTA并且成功
} eOTA_idxStatus_t;
//----------------------------------这里是移植气压计的commonTopLevel.h文件 END-----------------------------

#endif /*SYSTEM_CONFIG_H_*/
