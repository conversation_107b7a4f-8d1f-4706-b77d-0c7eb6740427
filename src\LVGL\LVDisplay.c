/*
 * Copyright (c) 2018 Jan <PERSON> <<EMAIL>>
 *
 * SPDX-License-Identifier: Apache-2.0
 */
#ifdef CONFIG_DGLAB_LVGL_DISPLAY_ST7789
#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/display.h>
#include <zephyr/drivers/gpio.h>
#include <lvgl.h>
#include <stdio.h>
#include <string.h>
#include <zephyr/kernel.h>
#include <lvgl_input_device.h>
#include <lvgl_mem.h>
#include <lv_demos.h>

#include "Game_Common.h"

#define LOG_LEVEL CONFIG_LOG_DEFAULT_LEVEL
#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(LVGL,LOG_LEVEL_WRN);

#define BLE_NUM 6
typedef enum
{
	eBLE_35X35_BREAK,
	eBLE_35X35_FINISH,
	eBLE_35X35_SCAN,
}eIMG_DISP_TYPE;
struct LCD_Display
{
	uint8_t BLE_IMG_Updata_Flag[BLE_NUM];//图标更新状态
	eIMG_DISP_TYPE BLE_IMG_DISP_Type[BLE_NUM];//蓝牙图标状态
};
typedef struct LCD_INFO 
{
    bool is_initialized;    // 初始化状态
    const struct device *display_dev;  // 显示设备指针

	struct LCD_Display Display;//显示
}ST_LCD_INFO;
ST_LCD_INFO LCD;

#define UI_THREAD_STACK_SIZE 2560
#define UI_THREAD_PRIORITY (CONFIG_NUM_PREEMPT_PRIORITIES-1)//设置优先级低一点
extern void ui_thread(void *arg1, void *arg2, void *arg3);

K_THREAD_DEFINE(ui_display_id, UI_THREAD_STACK_SIZE, 
				ui_thread, NULL, NULL, NULL,
				UI_THREAD_PRIORITY, 0, 0);


void ui_thread(void *arg1, void *arg2, void *arg3)
{
    static uint32_t time_interval_1s = 0;//间隔1秒
    // 获取显示设备
    // const struct device *display_dev;
    LCD.display_dev = DEVICE_DT_GET(DT_CHOSEN(zephyr_display));
    if (!device_is_ready(LCD.display_dev)) {
        LOG_ERR("Display device not ready, aborting test");
        return;
    }
	else
	{
		LOG_INF("Display Read OK");
	}

    // 获取活动屏幕并清除
    lv_obj_t *screen = lv_screen_active();
    lv_obj_clean(screen);

    // 声明三个图像对象
    // LV_IMG_DECLARE(BLE_35X35_BREAK);
    // LV_IMG_DECLARE(BLE_35X35_FINISH);
    // LV_IMG_DECLARE(BLE_35X35_SCAN);  
    LV_IMG_DECLARE(ni64x64);
    LV_IMG_DECLARE(shuang64x64);
    LV_IMG_DECLARE(shun64x64);

    #if 0
    // 计算每行的高度（假设屏幕高度为240像素）
    int row_height = 240 / 6;
	// int value_opa = 0;//透明度
    // 创建6行 
    for (int i = 0; i < 6; i++) 
	{
        // 创建横线
        lv_obj_t *line = lv_line_create(screen);  // 创建一个线对象
        static lv_point_precise_t line_points[] = {{0, 0}, {240, 0}};  // 定义线的起点和终点
        lv_line_set_points(line, line_points, 2);  // 设置线的点
        lv_obj_align(line, LV_ALIGN_TOP_LEFT, 0, i * row_height);  // 对齐线对象

        // 创建竖线
        lv_obj_t *vline = lv_line_create(screen);  // 创建一个竖线对象
        lv_point_precise_t vline_points[] = {{0, 0}, {0, row_height}};  // 定义竖线的起点和终点
        lv_line_set_points(vline, vline_points, 2);  // 设置竖线的点
        lv_obj_align(vline, LV_ALIGN_TOP_LEFT, 30, i * row_height);  // 对齐竖线对象

        // // 创建图像后的竖线
        // lv_obj_t *vline_after_img = lv_line_create(screen);  // 创建一个竖线对象
        // lv_line_set_points(vline_after_img, vline_points, 2);  // 设置竖线的点
        // lv_obj_align(vline_after_img, LV_ALIGN_TOP_LEFT, 70, i * row_height);  // 对齐竖线对象
        
		// 创建序号标签
        lv_obj_t *label = lv_label_create(screen);  // 创建一个标签对象
        char num_str[3];  // 创建一个字符数组来存储序号
        snprintf(num_str, sizeof(num_str), "%d", i + 1);  // 将序号转换为字符串
        lv_label_set_text(label, num_str);  // 设置标签文本
        lv_obj_align(label, LV_ALIGN_TOP_LEFT, 5, i * row_height + 5);  // 对齐标签对象

		// 显示BLE_35X35_SCAN图像
        lv_obj_t *img = lv_img_create(screen);
        lv_img_set_src(img, &BLE_35X35_SCAN);
        
        // 将图像居中显示在每行的第二栏中
        lv_obj_align(img, LV_ALIGN_TOP_LEFT, 35, i * row_height + (row_height - 35) / 2);
		for(uint8_t i = 0; i < BLE_NUM; i++)
		{
			LCD.Display.BLE_IMG_DISP_Type[i] = eBLE_35X35_SCAN;

		}
    }

    // lv_obj_t *img = lv_img_create(screen);
    // lv_img_set_src(img, &BLE_35X35_SCAN);
    // 更新LVGL并打开显示
    lv_timer_handler();
    display_blanking_off(LCD.display_dev);
    #endif
    static lv_obj_t *time_label     = NULL;

    // 首次创建标签
    if (time_label == NULL)
    {
        time_label = lv_label_create(screen);
        lv_obj_align(time_label, LV_ALIGN_CENTER, 0, 0);
        lv_obj_set_style_text_font(time_label, &lv_font_montserrat_24, 0);
        lv_label_set_text(time_label, "");  // 设置标签文本
    }

    
    

    // 更新LVGL并打开显示
    // lv_timer_handler();
    display_blanking_off(LCD.display_dev);
    // 主循环
	while (1) 
	{
        //----------------------------------显示连接------------------------------------------
        {
          // 每秒更新一次
          if(GAME_INFO.Game_Play.Time_Over >0)//开锁时间--
          {
            if (k_uptime_get_32() - time_interval_1s >= 1000)
            {
                // 计算时分秒
                uint32_t hours   = GAME_INFO.Game_Play.Time_Over / 3600;
                uint32_t minutes = (GAME_INFO.Game_Play.Time_Over % 3600) / 60;
                uint32_t seconds = GAME_INFO.Game_Play.Time_Over % 60;

                // 更新显示
                char time_str[10];
                snprintf(time_str, sizeof(time_str), "%02d:%02d:%02d", hours, minutes, seconds);
                lv_label_set_text(time_label, time_str);

                time_interval_1s = k_uptime_get_32();
            }
          }
          else
          {
            lv_label_set_text(time_label, "Unlocking");
          }
          //显示状态

        } 
         
		//----------------------------------搜索------------------------------------------
        #if 0
		{
            static uint8_t Time_Updata = 0;
            static int16_t Direction = 0;
            static bool MovingRight = true;

            Time_Updata++;
            if (Time_Updata >= 10) // 100ms (10 * 10ms)
            {
                Time_Updata = 0;

                // 更新每行的图像
                for (int i = 0; i < BLE_NUM; i++)
                {
                    if (LCD.Display.BLE_IMG_DISP_Type[i] == eBLE_35X35_SCAN)
                    {
                        lv_obj_t *img = lv_obj_get_child(screen, 3 + i * 4); // 获取每行的图像对象
                        if (img != NULL)
                        {
                            // 设置透明度为70%
                            lv_obj_set_style_img_opa(img, 178, 0); // 255 * 0.7 ≈ 178

                            // 更新位置
                            if (MovingRight)
                            {
                                Direction++;
                                if (Direction >= 150)
                                    MovingRight = false;
                            }
                            else 
                            {
                                Direction--;
                                if (Direction <= 0)
                                    MovingRight = true;
                            }
                            // 更新图像位置
                            lv_obj_align(img, LV_ALIGN_TOP_LEFT, 35 + Direction, i * (240 / 6) + ((240 / 6) - 35) / 2);
                        }
                    }
                }
            }
		}
        #endif

		
		// 定期更新LVGL
		lv_timer_handler();
		// 短暂休眠以节省资源
		k_sleep(K_MSEC(10));
	}
}

#endif
