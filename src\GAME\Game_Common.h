/**
 * @file Game_Common.h
 * @brief 玩法相关处理
 *
 *
 * @date 2025-07-07
 * <AUTHOR>
 */
#ifndef __GAME_COMMON_H__
#define __GAME_COMMON_H__
#include <stdint.h>



#define UNLOCKING_TIME_MAX_S  (1*60*60) //1小时
#define GAME_EVENT_ID_MAX 23 //触发ID最大数量

enum
{
  eTemp_Strength_Way_Fixed = 0x01,   // 固定
  eTemp_Strength_Way_Random = 0x02,  // 随机
  eTemp_Strength_Way_Parameter = 0x03, // 参数决定
  eTemp_Strength_Way_Gradient = 0x04,  // 渐变
};
typedef union
{
  uint8_t All_Arrays[19];  // 总字节大小，用于存储设备信息
  struct
  {
    uint8_t ID;  // 有效范围：1-24

    uint8_t Skip_Delay : 4,   // 停止/跳过延时启动游戏(后半字节)
            EventTime_Again : 4;  // 触发生效期间是否允许重新触发（前半字节）

    uint8_t COM_A_EN;  // A通道使能  1:和郊狼一样的基本触发模式 
    uint8_t COM_B_EN;  // B通道使能

    /*临时 一次性结果
    变化方式：
    0x00：功能未启用
    0x01：固定，固定情况下使用第一个变化值的数据，不使用第二个
    0x02：随机
    0x03：参数决定
    0x04到0xFF：渐变，0x04代表每0.1秒变化一次，0x05代表每0.2秒变化一次，以此类推。
    */
    int8_t Temp_Strength_A_Way;   // 变化方式（1字节）    
    int8_t Temp_Strength_A_Last;  // 第二个变化值（1字节）
    int8_t Temp_Strength_A;       // 第一个变化值（1字节)
    int8_t Temp_Strength_B_Way;   // 变化方式（1字节）
    int8_t Temp_Strength_B_Last;  // 第二个变化值（1字节）
    int8_t Temp_Strength_B;       // 第一个变化值（1字节)
    
    int8_t Strength_A_Last;  // A第二个变化值（1字节）APP:触发结果 强度永久改变
    int8_t Strength_A;       // A第一个变化值（1字节）APP:触发结果 强度永久改变 A通道
    int8_t Strength_B_Last;  // B第二个变化值（1字节）APP:触发结果 强度永久改变
    int8_t Strength_B;       // B第一个变化值（1字节）APP:触发结果 强度永久改变 B通道

    int16_t GameTime_Last;  // 第二个变化值（2字节）游戏时间改变（4字节）秒计算
    int16_t GameTime;       // 第一个变化值（2字节）00000（4字节）秒计算

    uint8_t EventTime;  // 触发生效时间（1字节）
  } Data;
}GAME_EVENT_t;  // 触发结果的设置

typedef union
{
  uint8_t All_Arrays[13];  // 总字节大小，用于存储设备信息
  struct
  {
    //延时启动（2字节）
    uint16_t  Conf_Time_Delay_Game_Start;  // 延迟启动游戏"范围：从1到65535（秒数）数据为0代表不启用"
    //基础强度设置（2字节）
    uint8_t Strength_A;//"A通道数值（1字节）范围：0-200 如果值是0-200以外代表功能未启用" 
    uint8_t Strength_B;//"B通道数值（1字节）
    //AB通道背景波形（2字节）
    uint8_t Waveform_A;//A通道背景波形（1字节）
    uint8_t Waveform_B;//B通道背景波形（1字节）
    //游戏时长（6字节）
    uint16_t Statr_Time_A;  // 起始定时时间1 Conf_Time_Remaining 总游戏剩余时间秒数 0xffff 不限定时间
    uint16_t Statr_Time_B;  // 起始定时时间2
    uint16_t MAX_Time;      // 最大定时时间
    //游戏中不能通过滚轮调节强度&不能长按滚轮关机（1字节）
    uint8_t Button; 
  }Data;
}GAME_START_STOP_CONF_t;//游戏启动停止设置
//玩法状态
typedef enum
{
  eInitial = 0x01,  // 初始状态
  eDelay   = 0x02,  // 延时中
  eUndeway = 0x03,  // 玩法生效中
} eGame_State_t;

//巡回犬模式
typedef enum
{
  eRandom = 0x01,  // 随机
  eLoop   = 0x02,  // 循环
  eGoTo   = 0x03,  // 往返
} eHover_Dog_Mode_t;

//游戏剩余时间计数模式
typedef enum
{
  eTime_Over_Init =0,
  eAddition = 0x01,     // 固定增加速度
  eAddition_Parameter=0x03,  // 参数增加速度
  eSubtraction,         // 固定减少
  eStop,                // 停止计数
} eTime_Over_Mode_t;

//巡回犬模式 触发ID类型
typedef enum
{
  eNone=0x00,//初始值
  eAwaitTrigger,//等待触发
  eSubDevice,//子设备触发
  eDisconnect,//断开连接 
  eAwaitNotify,//设备离线等待通知

} eHover_Dog_Triger_ID_Type_t;


//游戏信息
typedef struct
{
  eGame_State_t Game_State;             // 玩法状态（1字节）

  struct 
  {
    // uint16_t      Conf_Time_Remaining;         // 总游戏剩余时间秒数 0xffff 不限定时间
    // uint32_t      Conf_Time_Over;              // 游戏结束时间
    // uint16_t      Conf_Time_Delay_Game_Start;  // 延迟启动游戏"范围：从1到65535（秒数）数据为0代表不启用"

    uint16_t      Time_Remaining;         // 总游戏剩余时间秒数 0xffff 不限定时间
    uint32_t      Time_Over;              // 游戏结束时间
    uint16_t      Time_Delay_Game_Start;  // 延迟启动游戏"范围：从1到65535（秒数）数据为0代表不启用"
    
    uint8_t       Time_Over_Stop_Falg:1; // 游戏剩余时间停止标志 
    eTime_Over_Mode_t Time_Over_Mode;    // 游戏剩余时间计数模式
    int8_t        Time_Over_Addition_Parameter; // 游戏剩余时间增加参数 


    // uint32_t      Unlocking_Time;         // 开锁时间（4字节）
    struct 
    {
      uint8_t Start_EN:1;       // 使能巡回犬模式
      uint8_t Mode_Start_Flag:1;// 巡回犬模式触发标志 游戏玩法开始&&Hover_Dog_EN==1 才能真的开始
      eHover_Dog_Mode_t Mode;   // 巡回犬模式
      uint8_t Number_Config;    // 巡回次数设置 一共要走多少次单程才会开锁 
      uint8_t Number_Finish;    // 完成巡回次数 记录当前完成的次数
      // uint8_t Triger_Count;     // 巡回犬触发计数 
      uint8_t Triger_ID;        // 巡回犬触发ID 应当按下的按键值 触发后清零
      // uint8_t Triger_ID_Last;   // 巡回犬触发ID上一次值
      eHover_Dog_Triger_ID_Type_t  Triger_ID_Type;   // 触发类型
      uint8_t Goto_Mode_Index   ;          // 往返模式下当前索引
      int8_t Goto_Mode_Dir_Flag ;          // 往返模式当前方向（+1 或 -1） 需要初始化=1

      uint8_t Off_Line_Mode_AI_OFF : 1;  // 离线模式关闭 0:智能跳过掉线的配件 1:不考虑掉线的配件
      uint8_t Off_Line_ID;           // 离线ID

    }Hover_Dog;

    
  }Game_Play;//玩法有效时间设置 

  struct 
  {
    uint8_t  Count;       // 有效设备数量 按顺序存信息
    uint32_t ID_BitFLag;  // 有效设备位标志 (8位) 每一位代表一个设备是否有效 给灯分配颜色用
    GAME_EVENT_t Config[GAME_EVENT_ID_MAX];  // 触发结果的设置  有效范围：1-24
  }Game_EVENT;//手机APP发送的玩法ID

  GAME_START_STOP_CONF_t Game_START_STOP_CONF;
}ST_GAME_INFO;
extern ST_GAME_INFO GAME_INFO;


/// @brief 游戏玩法初始化
void Game_Common_Init(void);

#endif