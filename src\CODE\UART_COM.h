
/**
 * @file UART_COM.h
 * @brief 串口通信数据处理
 *
 * 处理串口上下发的对应指令
 *
 * @date 2025-07-11
 * <AUTHOR>
 */
#ifndef __UART_COM_H__
#define __UART_COM_H__
#include "stdint.h"
#include <zephyr/kernel.h>
//--------------------串口的一些设置---------------------------
#define UART_COM_RX_BUFF_SIZE 64  // 接收缓冲区大小
//----------------与子设备（STC）通信 协议----------------------
// 数据包格式:MARK(1B) LEN(1B) CMD(1B) DATA(?B) CRC(1B) EOP(1B)

typedef enum
{
  MARK_SEND      = 0xA5,  // 由顶层对底层发起时固定为0xa5
  MARK_RECEPTION = 0xAA,  // 由底层对顶层发起时固定为0xaa
} eMARK_t;

// 系统状态查询指令
#define CMD_SYS_CHECK            0x00  // 系统状态查询指令
#define CMD_GET_GLOBAL_KEYWORD   0x00  // 获取全局关键字
#define CMD_GET_FLAG_STATUS      0x01  // 获取标志位
#define CMD_GET_SN_CIPHER        0x0E  // 获取序列号和密文(预留32字节)
#define CMD_SET_MCU_POWER_STATE  0x0F  // 设置MCU省电状态(MCU可工作于完全休眠/自动低功耗/完全活跃这3种状态)

// 硬件控制指令
#define COM_DEV_CONNECT          0x10  // 硬件控制指令
#define CMD_SET_UART_WATCHDOG    0x10  // 设置串口复位看门狗(串口在设定时间内没收到合法包则底层MCU复位)
#define CMD_READ_ANALOG_PARAMS   0x20  // 读取模拟量参数(电池电压/线圈异常状态判定电压/线圈温度)
#define CMD_SET_LOCK_STATE       0x30  // 设置锁定状态(上锁逻辑开关)
#define CMD_READ_ENCODER_POS     0x31  // 读取编码器实时位置
#define CMD_SET_SOUND_SYNTH      0x40  // 设置声音合成器(音阶(频率)/占空比/音量/持续时间)

// 错误码定义
#define COM_ERR_CODE             0xE0  // 错误码定义
#define ERR_INVALID_LENGTH       0xEC  // 长度非法
#define ERR_INVALID_CMD          0xED  // 指令非法
#define ERR_CRC_ERROR            0xEE  // CRC错误
#define ERR_INVALID_VALUE        0xEF  // 设置值非法

// FLASH操作指令
#define CMD_FLASH_CONNECT        0xF0  // FLASH操作指令
#define CMD_SET_LOCK_CTRL_PARAMS 0xF1  // 设置"锁定控制器"参数(位置状态机恒电流值/运行时长/重试次数;非易失RW,OTA时数据保留)
#define CMD_FLASH_BLOCK0_RW      0xF8  // FLASH块0的信息读写(非易失RW,OTA时数据丢失变为0xFF)
#define CMD_FLASH_BLOCK1_RW      0xF9  // FLASH块1的信息读写(非易失RW,OTA时数据保留)
#define CMD_FLASH_BLOCK2_WO      0xFA  // FLASH块2的信息读写(非易失WO,与硬件绑定有关;OTA时数据保留)
#define CMD_FLASH_BLOCK3_WO      0xFB  // FLASH块3的信息读写(非易失WO,与硬件绑定有关;OTA时数据保留)
#define CMD_READ_EVENT_COUNTER   0xFC  // 关键事件计数器读取(非易失RO,一次性熔丝效果;OTA时数据保留)
#define CMD_WRITE_FACTORY_FUSE   0xFD  // 出厂标志熔丝位写入(非易失RW,标志具有OTP特性;OTA时数据保留)


typedef struct 
{
  struct k_work Work_Reception_Finish;  // 接收完成队列
  uint8_t Rx_Data_Buff[UART_COM_RX_BUFF_SIZE]; //接收数据缓存
}ST_UART_COM_t;
extern ST_UART_COM_t UART_COM;


/// @brief 串口发送子设备通信数据
int8_t UART_COM_Send_SubDevice_Data(eMARK_t mark,uint8_t cmd, const uint8_t *data, uint8_t len);

int UART_COM_Init(void);

#endif  // __UART_COM_H__