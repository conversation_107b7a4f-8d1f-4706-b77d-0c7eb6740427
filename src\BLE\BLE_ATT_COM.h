/**
 * @file BLE_COM.h
 * @brief 蓝牙ATT通信数据处理
 *
 * 处理手机APP上下发的对应蓝牙ATT指令
 *
 * @date 2025-06-23
 * <AUTHOR>
 */
#ifndef __ATT_COM_H__
#define __ATT_COM_H__  // 业务蓝牙ATT指令

#include <stdint.h>
#include <zephyr/kernel.h>
#include <string.h>
#include <zephyr/bluetooth/addr.h>
#include "BLE_MACList.h"



//接收APP指令
#define Receive_ATT_SCAN         0x01  // 扫描指令
#define Receive_ATT_FORWARD      0x60  // 配件指令透传
#define Receive_ATT_UNBIND_COLOR 0x08  // 解绑指定颜色配件
#define Reply_ATT_UNBIND_COLOR_Finish 0x09  // 回复解绑指定颜色配件完成
#define Reply_ATT_CHACK_ALL_SUBDEVICE 0XFF  // 查询所有配件玩法配置信息
#define Reply_ATT_CHACK_ALL_EVENT 0xFB      // 0xFB查询全部触发结果的设置
#define Reply_ATT_CHACK_START_STOP 0xFD      // 0xFD查询启动/停止设置
#define SET_GAME_CONFIG          (uint8_t)(0x50)  // 设置配件的玩法运行模式(1字节, 决定使用哪些传感器、硬件环境配置等)及其对应的触发配置数据(14字节, 共用)


//接收APP玩法指令
#define Receive_Game_Start        0x11  // APP通过指令启动配件玩法（可以在配件玩法工作中生效，但无意义）
#define Receive_Game_Stop         0x12  // 0x12 停止配件玩法（强制结束配件玩法） 
#define Receive_Game_SetSubDevice 0x30  // 0x30/修改配件触发设置（配件玩法工作中不生效）
#define Receive_Game_Event        0x20  // 0x20增加/修改指定触发ID的触发结果（配件玩法工作中不生效）
#define Receive_Game_DeleteID     0x28  // 0x28 删除指定ID触发结果（配件玩法工作中不生效）
#define Receive_Game_Config       0x40  // 0x40 启动/停止设置（配件玩法工作中不生效）

//响应APP玩法指令
#define Reply_Game_Start          0x10  // 回复0x11
#define Reply_Game_SetSubDevice   0x31  // 回复0x30
#define Reply_Game_Event          0x21  // 回复0x21
#define Reply_Game_DeleteID       0x29  // 回复0x29
#define Reply_Game_Config         0x41  // 回复0x40 

//----------接收子设备玩法指令---------
// 数据包类型id 0x51 +颜色（1字节）+子设备类型（1字节）+电量百分比（1字节）
#define Receive_Game_CTL_SubDevice_State 0x51  // 回复0x51→子设备返回自身状态
// 数据包类型id 0x53 +颜色（1字节）+自身SN号（4字节）
#define Receive_Game_CTL_Colour_SN       0x53  // 回复0x53→作为从机时，连接上之后自动发送自己的颜色，以及SN号
// 数据包类型id 0x5A +自身颜色（1字节）+事件ID（1字节）+参数（1字节）
#define Receive_Game_Event_Trigger       0x5A  // 回复0x5A→事件触发:
// 数据包类型id 0x5B +自身颜色（1字节）+事件ID（1字节）
#define Receive_Game_Event_Cancel        0x5B  // 回复0x5B→触发取消:
// 数据包类型id 0x5C +自身颜色（1字节）+事件ID（1字节）+参数（1字节）
#define Receive_Game_Updata              0x5C  // 回复0x5C→参数更新:

// 响应APP指令
#define Reply_CMD_HEADER_RSP     (uint8_t)(0x51)  //无意义, 兼容线上协议
// 所有ATT指令错误码
typedef enum 
{
  eERRNO_ATT_invalidCMD  = 0x01,  // 命令类型不理解(指令头不在定义的范围内)
  eERRNO_ATT_invalidLEN  = 0x02,  // 命令格式(主要是长度)不正确
  eERRNO_ATT_invalidSTA  = 0x03,  // 命令当前状态应用(工作模式互斥或缺乏必要的上下文)
  eERRNO_ATT_invalidTGT  = 0x04,  // 命令请求操作的目标不存在(目标可能是配置项、子设备或文件)
  eERRNO_ATT_failedAsync = 0x05,  // 上一个异步操作失败了(上一个请求可能是需要长耗时的操作, 例如文件系统自检等)
  eERRNO_ATT__reserveA   = 0x06,  // 保留码 (资料缺失, 意义不明)
  eERRNO_ATT_Forbidden   = 0x07,  // 拒绝执行, 因为缺乏权限或身份验证失败 (启用了设备绑定功能)
} __attribute__((packed)) eAtt_Err;
//收到的命令出现各种错误时，将会回复通用错误码
//0xE0（1字节）+ 指令类型ID<即上位机发送过来的指令类型报头>(1字节) + 通用错误码类型(1字节)  
#define Reply_ERROR_Array(id,err) (const uint8_t[] )\
   {0xE0, id, err}

// 所有ATT指令码
// typedef enum 
// {
//   eATT_New_Equipment = 0x02,  //成功绑定了一个未绑定过的合法配件
// } __attribute__((packed)) eAtt_err_t;

// 子设备颜色
typedef enum  
{
  eSUBDEVICE_COLOR_YELLOW = 0x01, // 黄
  eSUBDEVICE_COLOR_RED    = 0x02, // 红
  eSUBDEVICE_COLOR_PURPLE = 0x03, // 紫
  eSUBDEVICE_COLOR_BLUE   = 0x04, // 蓝
  eSUBDEVICE_COLOR_CYAN   = 0x05, // 青
  eSUBDEVICE_COLOR_GREEN  = 0x06, // 绿
  eSUBDEVICE_COLOR_WHITE  = 0x07  // 白 
}  __attribute__((packed))eSubDevice_Color;

// 子设备类型
typedef enum
{
  eSUBDEVICE_TYPE_ELECTROSHOCKER_3_0 = 0x10,  // 3.0电击器
  eSUBDEVICE_TYPE_SMART_LOCK         = 0x20,  // 智能锁
  eSUBDEVICE_TYPE_BUTTON             = 0x01,  // 按钮
  eSUBDEVICE_TYPE_COLLAR             = 0x30,  // 项圈
  eSUBDEVICE_TYPE_BAROMETER          = 0x40,  // 气压计
  eSUBDEVICE_TYPE_NEW_BUTTON_2_0     = 0x50,  // 新的按钮2.0
  eSUBDEVICE_TYPE_BUTTON_OFFICIAL    = 0x03,  // 按钮正式版
  eSUBDEVICE_TYPE_BAROMETER_ABS      = 0x40,  // 气压计绝压型版本
  eSUBDEVICE_TYPE_VIBRATOR_BOX_4CH   = 0x60,  // 震动器控制盒(4ch)
  eSUBDEVICE_TYPE_VIBRATOR           = 0x70   // 负鼠(振动控制器)
} __attribute__((packed)) eSubDevice_Type;

// 连接状态
typedef enum
{
  eCONN_STATUS_ONLINE  = 0x01, // 在线
  eCONN_STATUS_OFFLINE = 0x02  // 离线
} __attribute__((packed)) eConn_Status;


//*********************************接收0X01 扫描指令 相关回复*********************************
// 回复0x02→成功绑定了一个未绑定过的合法配件并停止扫描，将该设备信息发给APP 
//共5字节，0x02（1字节） +颜色（1字节）+子设备类型（1字节）+电量百分比（1字节）+连接状态（1字节）
#define Reply_Bind_New_Equipment_Array(_color, _type, _soc, _status) (const uint8_t[] )\
  {0x02, _color, _type, _soc, _status}  // 示例数据: 颜色为黄色(0x01), 子设备类型为1, 电量百分比为100%, 连接状态为未连接(0x00)

// 回复0x03→扫描超时停止 
#define Reply_Bind_Scan_Time_Out_u8  (const uint8_t[]){0x03}

// 回复0x04→对象不存在，指需要解绑的配件，或者要删除的触发结果，或者需要修改的配件触发配置（触发条件）不存在。
#define Reply_OBJ_None_u8  (const uint8_t[]){0x04}

// 回复0x04→ 扫描并且成功绑定到了之前已经绑定过的配件 
//共5字节，0x04（1字节）+颜色（1字节）+子设备类型（1字节）+电量百分比（1字节）+连接状态（1字节）
#define Reply_Bind_Old_Equipment_Array(_color, _type, _soc, _status) (const uint8_t[] )\
  {0x04, _color, _type, _soc, _status}  // 示例数据: 颜色为红色(0x02), 子设备类型为2, 电量百分比为80%, 连接状态为在线(0x01)

// 指令包长度不对，则回复0xE0（1字节） +0x01(1字节) +0x02通用错误码(1字节)  
#define Reply_Bind_ATT_ERR_Array(_att_err) (const uint8_t[] )\
  {0x0E, 0x01, eERRNO_ATT_inva_att_errlidLEN}  // 示例数据: 错误码为长度不正确

// 回复0x10→ 上传当前处于的工作状态和倒计时 
// 共4字节，0x10（1字节）+玩法状态（1字节）+剩余时间以秒为单位的整数(2字节)
#define Reply_Game_Start_Array(_state,_id)  (const uint8_t[]){Reply_Game_Start, _state,(uint8_t)((_id) >> 8),(uint8_t)(_id) }

//*********************************接收0x08 解绑指定颜色配件 相关回复*********************************
// APP端：这条指令发送出去后显示模态，禁止其他操作，直到收到0x09回复之后才会退出模态。
#define Reply_Bind_ATT_Unbind_Colo(_color) (const uint8_t[] ){0x09,_color}

//*********************************接收0xFF查询所有配置*********************************
// 每个包20字节，0xF3（1字节）+颜色（1字节）+子设备类型（1字节）+电量百分比（1字节）+连接状态（1字节）+触发设置（15字节）

//*********************************上报0x0F 配件电量或者连接状态发生变化时上传 *********************************
// 0x0F→配件电量或者连接状态发生变化时上传
//共5字节，0x0F（1字节）+颜色（1字节）+子设备类型（1字节）+电量百分比（1字节）+连接状态（1字节）
#define Update_Equipment_Status_Array(_color, _type, _soc, _status) (const uint8_t[] )\
  {0x0F, _color, _type, _soc, _status}  // 示例数据: 颜色为绿色(0x06), 子设备类型为3, 电量百分比为50%, 连接状态为在线(0x01)



//---------------------------------------主动上报的内容------------------------------------------
// (定时及电量变化)状态信息_4 响应头(0x51)_1, 配件ID_1, 配件类型_1, 电量百分比_1
#define Reply_Set_Game_Config_Length (uint8_t)(4)
#define Reply_Set_Game_Config(_id_subDviceColor, _id_subDivceType, _data_battLevel) (const uint8_t[] ){Reply_CMD_HEADER_RSP,_id_subDviceColor,_id_subDivceType,_data_battLevel}


// 回复0x29→ 删除成功
// 共2字节，0x29（1字节）+成功删除的触发事件ID（1字节）
#define Reply_Game_DeleteID_Array(_id)  (const uint8_t[] ){Reply_Game_DeleteID,_id}

//--------------------------------------游戏玩法---------------------------------------------------------










// extern struct k_msgq Queue_PPL_WrIte;
extern ST_Ble_MACList BLE_List_Validity;
extern ST_Ble_MACList BLE_List_Offline ;      // 离线设备列表
void BLE_ATT_COM_FUNC_Init(void);
/// @brief 从flash上更新白名单列表
uint8_t Updata_Flash_From_WhiteList(void);
/// @brief 通过子设备ID获取索引
int8_t BLE_ATT_Get_CTL_CONN_Index_Fo_SubDevice(uint8_t subDevice);

/// @brief 增加一个值在数组后面
int BLE_ATT_Array_store_first_zero(uint8_t *arr, uint8_t value );

/// @brief 删除数组中的第一个元素，并将后续元素前移
void BLE_ATT_Array_remove_first_and_shift(uint8_t *arr );


///@brief  循环移动bt_addr_le_t类型的数组，将[0]数据移动到第一个空数据位置，如果没有空位则整体循环移位
void BLE_ATT_bt_addr_le_array_shift(bt_addr_le_t *arr);

#endif  // __ATT_COM_H__