
/**
 * @file UART_COM_DEV.h
 * @brief 串口通信数据处理
 *
 * 处理串口上下发的对应指令
 *
 * @date 2025-07-11
 * <AUTHOR>
 */
#include "UART_COM.h"
#include <zephyr/drivers/uart.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/sys_clock.h>

#include "System_Config.h"
#include "GPIO_CONF.h"
#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(UART, LOG_LEVEL_DBG);

ST_UART_COM_t UART_COM;
const struct device *UART_COM_DEV = DEVICE_DT_GET(DT_NODELABEL(uart21));
// static uint8_t rx_buf[1024] = {0}; //A buffer to store incoming UART data

// 定义串口发送数据队列结构
typedef struct {
    uint8_t *data;     // 动态分配的数据缓冲区
    uint16_t len;      // 数据长度
} uart_data_t;
K_MSGQ_DEFINE(uart_tx_msgq, sizeof(uart_data_t),3, 4);//声明一个数据发送消息队列 用于延迟发送
// K_MSGQ_DEFINE(uart_rx_msgq, sizeof(uart_data_t),3, 4);//声明一个数据接收消息队列 

struct k_work_delayable send_subdevice_work;  // 延时工作队列，用延迟发送

/**
 * @brief 计算CRC校验值
 * @param data 数据指针
 * @param len 数据长度
 * @return CRC值
 */
static uint8_t Calculate_CRC(const uint8_t *data, uint8_t len)
{
  uint8_t crc = 0;
  for (uint8_t i = 0; i < len; i++)
  {
    crc ^= data[i];
  }
  return crc;
}

/// @brief 串口数据接收完成
static void Reception_Finish_handler(struct k_work *work) 
{
  uint8_t *data = UART_COM.Rx_Data_Buff;
  uint8_t len = data[1];// LEN(1B) 
  LOG_HEXDUMP_DBG(data, len+5, "RX data:");
  if(data[0] == MARK_RECEPTION)// MARK(1B)  检验
  {
    uint8_t cmd = data[2];// CMD(1B) 
    switch (cmd & 0xF0)
    {
      case CMD_SYS_CHECK:  // 系统状态查询指令
      {
        switch (cmd)
        {
          case CMD_GET_GLOBAL_KEYWORD:// 获取全局关键字
          {
          }
          break;
          case CMD_GET_FLAG_STATUS:  // 获取标志位
          {
          }
          break;
          case CMD_GET_SN_CIPHER:  // 获取序列号和密文
          {
          }
          break;
          case CMD_SET_MCU_POWER_STATE:  // 设置MCU省电状态
          {
          }
          break;
          default:
            break;
        }
      }
      break;
      case COM_DEV_CONNECT:  // 硬件控制指令
      {
        switch (cmd)
        {
          case CMD_SET_UART_WATCHDOG:// 设置串口复位看门狗
          {
          }
          break;
          case CMD_READ_ANALOG_PARAMS:  // 读取模拟量参数
          {
          }
          break;
          case CMD_SET_LOCK_STATE:  // 设置锁定状态
          {
          }
          break;
          case CMD_READ_ENCODER_POS:  // 读取编码器实时位置
          {
          }
        }
      }
      break;
      case COM_ERR_CODE:  // 错误码定义
      {
        switch (cmd)
        {
          case ERR_INVALID_LENGTH:// 长度非法
          {
          }
          break;
          case ERR_INVALID_CMD:  // 指令非法
          {
          }
          break;
          case ERR_CRC_ERROR:  // CRC错误
          {
          }
          break;
          case ERR_INVALID_VALUE:  // 设置值非法
          {
          }
          break;
          default:
            break;
        }
      }
      break;
      case CMD_FLASH_CONNECT:  // FLASH操作指令
      {
        switch (cmd)
        {
          case CMD_SET_LOCK_CTRL_PARAMS:// 设置"锁定控制器"参数
          {
          }
          break;
          case CMD_FLASH_BLOCK0_RW:  // FLASH块0的信息读写
          {
          }
          break;
          case CMD_FLASH_BLOCK1_RW:  // FLASH块1的信息读写
          {
          }
          break;
          case CMD_FLASH_BLOCK2_WO:  // FLASH块2的信息读写
          {
          }
          break;
          case CMD_FLASH_BLOCK3_WO:  // FLASH块3的信息读写
          {
          }
          break;
          case CMD_READ_EVENT_COUNTER:  // 关键事件计数器读取
          {
          }
          break;
          case CMD_WRITE_FACTORY_FUSE:  // 出厂标志熔丝位写入
          {
          }
          break;
          default:
            break;
        }

      }
      break;
      default:
        break;
    }
  }
}

/**
 * @brief 从队列取出数据并发送
 * @return 0成功，其他失败
 */
static int uart_tx_dequeue(void)
{
    uart_data_t tx_data;
    int ret;
    // 从队列中获取数据
    if (k_msgq_get(&uart_tx_msgq, &tx_data, K_NO_WAIT) != 0)
    {
      return -EAGAIN;
    }
    ret = uart_tx(UART_COM_DEV, tx_data.data, tx_data.len, SYS_FOREVER_MS);    // 发送数据
    // 释放内存
    k_free(tx_data.data);
    return ret;
}

/// @brief  发送队列
static void send_subdevice_handler(struct k_work *work)
{
  GPIO_CONF_Set_Awaken(~Config_Hardware_SubDevice_IRQ_GPIO_Trigger);//关闭
  uart_tx_dequeue();//从队列取出数据并发送
}



/**
 * @brief 串口发送子设备通信数据
 * @param cmd 命令字
 * @param data 数据指针
 * @param len 数据长度
 * @return 0成功，其他失败
 */
int8_t UART_COM_Send_SubDevice_Data(eMARK_t mark,uint8_t cmd, const uint8_t *data, uint8_t len)
{
  if (len > 250) return -1;  // 预留协议头尾字节
  // 检查队列是否已满
  if (k_msgq_num_used_get(&uart_tx_msgq) >= 64) 
  {
    LOG_ERR("TX queue is full");
    return -EAGAIN;
  }
  // 计算总长度: MARK + LEN + CMD  + CRC + EOP
  uint8_t total_len = len + 5;
  uart_data_t tx_data;
  tx_data.data = k_malloc(total_len);// 动态分配内存
  if (tx_data.data == NULL)
  {
    LOG_ERR("Failed to allocate TX buffer");
    return -ENOMEM;
  }
  // 拼接包
  uint8_t index = 0;
  tx_data.data[index++] = mark;
  tx_data.data[index++] = len;
  tx_data.data[index++] = cmd;
  if (data && len > 0)
  {
    memcpy(&tx_data.data[index], data, len);
    index += len;
  }
  // 计算并添加CRC
  tx_data.data[index++] = Calculate_CRC(&tx_data.data[2], len + 1);  // CMD+DATA
  tx_data.data[index++] = '\n';
  tx_data.len = total_len;
  // 将数据放入队列
  if (k_msgq_put(&uart_tx_msgq, &tx_data, K_NO_WAIT) != 0) 
  {
    k_free(tx_data.data);
    LOG_ERR("Failed to queue TX data");
    return -EAGAIN;
  }
  //---------------IRQ--------------------
  GPIO_CONF_Set_Awaken(Config_Hardware_SubDevice_IRQ_GPIO_Trigger);//开启
  k_work_schedule(&send_subdevice_work, K_MSEC(Config_Hardware_SubDevice_IRQ_Send_Time_mS));  // 启动延迟发送工作队列
  return 0;
}

/// @brief 接收到数据回调
static void SubDevice_cb(const struct device *dev, struct uart_event *evt, void *user_data)
{
 switch (evt->type) {
   case UART_TX_DONE:  // 整个发送缓冲区已传输
    //  LOG_DBG("UART_TX_DONE");
     break;
   case UART_TX_ABORTED:  // 由于超时或 uart_tx_abort ()调用，传输被中止
    //  LOG_DBG("UART_TX_ABORTED");
     break;
   case UART_RX_RDY:  // 接收到一些数据并且发生了接收超时（如果启用了 RX 超时）或接收缓冲区已满
    //  LOG_DBG("UART_RX_RDY");
     break;
   case UART_RX_BUF_REQUEST:  // 驱动程序请求下一个缓冲区以进行连续接收
    //  LOG_DBG("UART_RX_BUF_REQUEST");
     break;
   case UART_RX_BUF_RELEASED:  // UART 驱动程序不再使用该缓冲区
    //  LOG_DBG("UART_RX_BUF_RELEASE");
     break;
   case UART_RX_DISABLED:  // 每当接收器停止、禁用或完成其操作（接收缓冲区已满）时，都会生成此事件，并且可以使用 uart_rx_enable() 重新启用
   {
      uart_rx_enable(dev, UART_COM.Rx_Data_Buff, UART_COM_RX_BUFF_SIZE, 100);
      k_work_submit(&UART_COM.Work_Reception_Finish);//解析接收到的数据
      // LOG_DBG("UART_RX_DISABLED");
   }
    break;
   case UART_RX_STOPPED:  // RX 由于外部事件已停止
    //  LOG_DBG("UART_RX_STOPPED");
     break;
   default:
     break;
 }
}

/// @brief  串口初始化
int UART_COM_Init(void)
{
	const struct uart_config uart_cfg = 
	{
		.baudrate = 115200,
		.parity = UART_CFG_PARITY_NONE,
		.stop_bits = UART_CFG_STOP_BITS_1,
		.data_bits = UART_CFG_DATA_BITS_8,
		.flow_ctrl = UART_CFG_FLOW_CTRL_NONE
	};

	int err = uart_configure(UART_COM_DEV, &uart_cfg);
  if (err == -ENOSYS)
  {
    return -ENOSYS;
  }
  err = uart_callback_set(UART_COM_DEV, SubDevice_cb, NULL);
  if (err)
  {
    return err;
  }
	if (!device_is_ready(UART_COM_DEV)){//验证 UART 设备是否就绪。
		LOG_ERR("UART device not ready");
		return 1;
	}
	err =uart_rx_enable(UART_COM_DEV ,UART_COM.Rx_Data_Buff,UART_COM_RX_BUFF_SIZE,100);
	if (err)
  {
   LOG_ERR("uart_rx_enable");
  }

  k_work_init_delayable(&send_subdevice_work, send_subdevice_handler);      // 初始化PPl数据发送工作队列
  k_work_init(&UART_COM.Work_Reception_Finish, Reception_Finish_handler);  // 初始化写合法性工作队列

	return 1;
}
