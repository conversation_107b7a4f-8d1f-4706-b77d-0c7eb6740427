#ifndef CELL_CMD_PARSE_H_
#define CELL_CMD_PARSE_H_
#define BIND_PUBLIC_ADV_DEVICE 0x01  //  绑定可用设备，蓝牙名称包含 DGLAB 字段视为有效
// #define UNBIND_DEVICE 0x08  // 解绑指定颜色设备

// #define WHITE_LIST_MODE 0x0E // 白名单模式
// #define SET_AUTHORITY_BOND 0x0D // 设备绑定模式开关, 开启绑定模式后只有绑定的设备(设备需要具备有效IRK)可以连接. 设置16个字节的绑定目标,
// 全00表示关闭设备绑定模式、全FF表示启用启用设备绑定模式并自动记录IRK、其他值表示由APP指定IRK(不太好做而且意义不大, 先预留不做) #define GET_AUTHORITY_BOND 0x0C // 设备绑定模式查询, 返回记录的IRK,
// 用于确认设备IRK合法性以及是否正常更新

// #define GAME_START 0x11   // APP通过指令启动配件玩法（如果设置了延时启动那么就开始倒计时，如果设置了条件启动就等待条件）
// #define GAME_STOP 0x12    // 关闭配件玩法（强制结束配件玩法）

// #define EVENT_SETTING 0x20  // 事件配置
// #define EVENT_REMOVE 0x28  // 删除事件 20220216 改成0x28

// #define TRIGGER_SETTING 0x30  // 指定触发ID的触发结果

// #define GAME_SETTING 0x40  // 游戏配置

// #define WAVE_DOWNLOAD 0xA0 // 波形下载头条
// #define WAVE_DOWNLOAD_IS_READY 0xA1  // 已经准备好波形下载了
// #define WAVE_DOWNLOAD_DATA 0xA2      // 波形有效数据的包头
// // #define WAVE_DOWNLOAD_SUCCESS 0xA3 // 波形有效数据
// #define RESET_USER_WAVE 0xAF
// // #define CLEAN_USER_WAVE 0xAF

// #define LOAD_ALL_CONFIG 0xFF  // 查询所有配置

// #define LOAD_GAME_STATE 0xFA // 查询当前游戏状态 20220216 新增

// #define LOAD_GAME_SETTING 0xFD  // 查询游戏设置 20220216 新增

// #define LOAD_ALL_EVENT_SETTING 0xFB // 20220217 纠正
// #define LOAD_ALL_BOND_TRIGGER_SETTING 0xFC // 20220217 纠正

// #define REST_ALL_EVENT_AND_TRIGGER 0xFE  // 20220317 加入, 删除所有event跟触发条件

// #define MASTER_INIT_MSG 0x50
// #define CLEAR_ALL_PARAM 0x5F

// #define MSG_DIRECT_TO_SUB_DEVICE 0x60

// #define LEGAL_DEVICE_CHECK 0x72

// #define POWER_AND_WAVE 0xB0

// // 20231126 新增
// #define SET_SOFT_LIMIT_AND_BALANCE 0xBF

// // 20240123 新增
// #define SET_PW_PARAMS 0xBC

// // 测试用命令
// #define LOAD_EVENT_SETTING_BY_ID 0xEB // 根据event ID查询event详情 20220216 变更
// #define LOAD_TRIGGER_SETTING_BY_COLOR 0xEC // 根据按钮颜色查询trigger 20220216 变更
// #define GET_BUILDINFO 0xED // 查询固件编译信息 2024年8月22日 新增

// // #define PRINT_ONLINE_BUTTONS 0xD0
// // #define START_TRACING_LIVE_POWER 0xE1  // 强度实时上传开关
// // #define PRINT_BIND_BUTTONS 0xE2  // 打印绑定按钮

// #define SET_ABS_POWER_CHA 0xE3  // 设置通道A绝对强度
// #define SET_ABS_POWER_CHB 0xE4  // 设置通道B绝对强度

// #define QUERY_SILENT_MODE 0xE0  // 查询静谧模式
// #define SET_SILENT_MODE 0xE1    // 设置静谧模式

// #define FULL_FLOW_TEST 0xC0     // 流量测试
#endif /*CELL_CMD_PARSE_H_*/
